'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

type Theme = 'light' | 'dark';

interface ThemeProviderContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  systemTheme: Theme;
}

const ThemeProviderContext = createContext<ThemeProviderContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>('light');
  const [systemTheme, setSystemTheme] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);

  // Initialize theme on mount
  useEffect(() => {
    const initializeTheme = () => {
      try {
        // Detect system theme
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const detectedSystemTheme = mediaQuery.matches ? 'dark' : 'light';
        setSystemTheme(detectedSystemTheme);

        // Get saved theme or use system theme
        const savedTheme = localStorage.getItem('whop-contracts-theme') as Theme | null;
        const initialTheme = savedTheme || detectedSystemTheme;
        
        setThemeState(initialTheme);
        applyThemeToDocument(initialTheme);
        
        // Save theme if not already saved
        if (!savedTheme) {
          localStorage.setItem('whop-contracts-theme', initialTheme);
        }
      } catch (error) {
        console.warn('Failed to initialize theme:', error);
        setThemeState('light');
        applyThemeToDocument('light');
      }
    };

    setMounted(true);
    initializeTheme();
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    if (!mounted) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      const newSystemTheme = e.matches ? 'dark' : 'light';
      setSystemTheme(newSystemTheme);
      
      // Only update theme if user hasn't manually set a preference
      const savedTheme = localStorage.getItem('whop-contracts-theme');
      if (!savedTheme) {
        setThemeState(newSystemTheme);
        applyThemeToDocument(newSystemTheme);
        localStorage.setItem('whop-contracts-theme', newSystemTheme);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [mounted]);

  const applyThemeToDocument = (newTheme: Theme) => {
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(newTheme);
    
    // Also update data attribute for CSS selectors
    root.setAttribute('data-theme', newTheme);
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    applyThemeToDocument(newTheme);
    
    try {
      localStorage.setItem('whop-contracts-theme', newTheme);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  const value: ThemeProviderContextType = {
    theme,
    setTheme,
    systemTheme,
  };

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}