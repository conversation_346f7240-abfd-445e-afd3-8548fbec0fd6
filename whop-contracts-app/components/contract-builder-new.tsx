'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { getTemplateById, contractTemplates } from '@/lib/contract-templates';
import type { ContractTemplate } from '@/lib/contract-templates';
import { ContractData, ContractFormData } from '@/lib/types';
import { processContractTemplate, validateContractData, generateContractId } from '@/lib/contract-utils';
import { exportToPDF, exportToDocx } from '@/lib/export-utils';
import { InvoiceData, createWhopInvoice, generateInvoiceHTML, validateInvoiceData } from '@/lib/invoice-utils';
import { notificationService } from '@/lib/notifications';
import { showToast, copyToClipboard, ensureFullUrl } from './toast';
import { ArrowLeft, Eye, Save, Send, Download, FileText, DollarSign, Calendar, Plus, ChevronDown } from 'lucide-react';
import { Heading, Text, Button, TextField, TextArea, Switch, Calendar as FrostCalendar } from 'frosted-ui';

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface ContractBuilderProps {
  templateId?: string | null;
  user: User;
  userId: string;
  companyId?: string;
  onSave: (contract: ContractData) => void;
  onBack: () => void;
}

// Create dynamic schema based on template
const createValidationSchema = (template: any) => {
  const sellerInfoSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Valid email is required'),
    address: z.string().min(1, 'Address is required'),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const clientInfoSchema = z.object({
    name: z.string().min(1, 'Client name is required'),
    email: z.string().email('Valid client email is required'),
    address: z.string().min(1, 'Client address is required'),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const contractFieldsSchema = z.object(
    template?.fields.reduce((acc: any, field: any) => {
      if (field.required) {
        acc[field.id] = field.type === 'email' 
          ? z.string().email(`Valid ${field.label} is required`)
          : z.string().min(1, `${field.label} is required`);
      } else {
        acc[field.id] = z.string().optional();
      }
      return acc;
    }, {}) || {}
  );

  return z.object({
    templateId: z.string(),
    sellerInfo: sellerInfoSchema,
    clientInfo: clientInfoSchema,
    contractFields: contractFieldsSchema,
  });
};

export function ContractBuilderNew({ templateId = null, user, userId, companyId = 'temp_company', onSave, onBack }: ContractBuilderProps) {
  const [template, setTemplate] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [showInvoiceSection, setShowInvoiceSection] = useState(false);
  const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null);
  
  // State for interactive elements
  const [selectedClient, setSelectedClient] = useState<string>('Cameron Zoub');
  const [selectedContractType, setSelectedContractType] = useState<string>('Whop Design');
  const [availableTemplates, setAvailableTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [selectedTerms, setSelectedTerms] = useState<'standard' | 'custom'>('standard');
  const [enablePayments, setEnablePayments] = useState(true);
  const [contractDuration, setContractDuration] = useState<string>('30 days');
  const [selectedPricing, setSelectedPricing] = useState<'free' | 'one-time' | 'recurring'>('one-time');
  const [contractValue, setContractValue] = useState<string>('10.00');
  const [recurringPeriod, setRecurringPeriod] = useState<'weekly' | 'monthly' | 'yearly'>('monthly');
  const [currency, setCurrency] = useState<string>('USD');
  const [showContractOptions, setShowContractOptions] = useState(false);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [customerSearch, setCustomerSearch] = useState('');
  const [showDueDateDropdown, setShowDueDateDropdown] = useState(false);
  const [selectedDueDate, setSelectedDueDate] = useState('Due in 30 days');
  const [showCalendar, setShowCalendar] = useState(false);
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [showRecurringDropdown, setShowRecurringDropdown] = useState(false);

  // Customer state
  const [customers, setCustomers] = useState<Array<{id: string, name: string, email: string, address?: string, phone?: string}>>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  // Fetch customers from API
  const fetchCustomers = async () => {
    if (!userId) return;
    
    setLoadingCustomers(true);
    try {
      const response = await fetch(`/api/customers?authorId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setCustomers(data.customers);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoadingCustomers(false);
    }
  };

  // Save new customer
  const saveCustomer = async (name: string, email: string, address?: string) => {
    if (!userId) return;
    
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          address,
          authorId: userId
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Add the new customer to the list
        setCustomers(prev => [data.customer, ...prev]);
        return data.customer;
      }
    } catch (error) {
      console.error('Error saving customer:', error);
    }
  };

  // Initialize selected template
  useEffect(() => {
    if (!selectedTemplate && availableTemplates.length > 0) {
      const serviceAgreementTemplate = availableTemplates.find(t => t.id === 'service-agreement');
      const defaultTemplate = serviceAgreementTemplate || availableTemplates[0];
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [selectedTemplate, availableTemplates]);

  // Fetch customers on component mount
  useEffect(() => {
    fetchCustomers();
  }, [userId]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-customer-dropdown]')) {
        setShowCustomerDropdown(false);
      }
      if (!target.closest('[data-due-date-dropdown]')) {
        setShowDueDateDropdown(false);
      }
      if (!target.closest('[data-currency-dropdown]')) {
        setShowCurrencyDropdown(false);
      }
      if (!target.closest('[data-template-dropdown]')) {
        setShowTemplateDropdown(false);
      }
      if (!target.closest('[data-recurring-dropdown]')) {
        setShowRecurringDropdown(false);
      }
    };

    if (showCustomerDropdown || showDueDateDropdown || showCurrencyDropdown || showTemplateDropdown || showRecurringDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showCustomerDropdown, showDueDateDropdown, showCurrencyDropdown, showTemplateDropdown, showRecurringDropdown]);

  // Load available templates
  useEffect(() => {
    try {
      const templates = contractTemplates as unknown as ContractTemplate[];
      console.log('Available templates:', templates.map(t => ({ id: t.id, name: t.name })));
      setAvailableTemplates(templates);
      
      if (templateId) {
        console.log('Loading template with ID:', templateId);
        const template = templates.find(t => t.id === templateId);
        console.log('Loaded template:', template);
        if (template) {
          setTemplate(template);
          setSelectedTemplate(template);
        }
      } else {
        // Load default template if no templateId provided
        const defaultTemplate = templates.find(t => t.id === 'nda') || templates[0];
        if (defaultTemplate) {
          setTemplate(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  }, [templateId]);

  const validationSchema = createValidationSchema(template);
  
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
    trigger
  } = useForm<ContractFormData>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      templateId: templateId || 'default-invoice', // Use default template if none provided
      sellerInfo: {
        name: user.name || 'Whop Inc.',
        email: user.username,
        address: '1234 Innovation Drive, San Francisco, CA 94105',
        phone: '',
        businessName: 'Whop Inc.'
      },
      clientInfo: {
        name: '',
        email: '',
        address: '',
        phone: '',
        businessName: ''
      },
      contractFields: {}
    }
  });

  // Custom validation state to track when all required fields are filled
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Watch all form values to determine if form is valid
  const formValues = watch();
  
  // Check if form is valid whenever form values or template changes
  useEffect(() => {
    const checkFormValidity = async () => {
      if (!template) return;
      
      try {
        // Validate the current form data against the schema
        const currentData = getValues();
        
        // Check if all required client info fields are filled
        const clientInfoValid = currentData.clientInfo.name && 
                               currentData.clientInfo.email && 
                               currentData.clientInfo.address;
        
        // Check if all required contract fields are filled
        const contractFieldsValid = template.fields.every((field: any) => {
          if (!field.required) return true;
          const fieldValue = currentData.contractFields[field.id];
          return fieldValue && fieldValue.trim().length > 0;
        });
        
        // Check if template is selected
        const templateSelected = currentData.templateId && currentData.templateId !== 'default-invoice';
        
        const isValid = clientInfoValid && contractFieldsValid && templateSelected;
        setIsFormValid(isValid);
      } catch (error) {
        console.error('Error checking form validity:', error);
        setIsFormValid(false);
      }
    };
    
    checkFormValidity();
  }, [formValues, template, getValues]);

  // Update form when template changes
  useEffect(() => {
    if (template) {
      // Reset form with new template
      reset({
        templateId: template.id,
        sellerInfo: {
          name: user.name || 'Whop Inc.',
          email: user.username,
          address: '1234 Innovation Drive, San Francisco, CA 94105',
          phone: '',
          businessName: 'Whop Inc.'
        },
        clientInfo: {
          name: '',
          email: '',
          address: '',
          phone: '',
          businessName: ''
        },
        contractFields: {}
      });
    }
  }, [template, reset, user.name, user.username]);

  const onSubmit = async (data: ContractFormData) => {
    try {
      const contractData: ContractData = {
        id: generateContractId(),
        templateId: data.templateId,
        sellerInfo: data.sellerInfo,
        clientInfo: data.clientInfo,
        contractFields: data.contractFields,
        status: 'DRAFT' as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      onSave(contractData);
    } catch (error) {
      console.error('Error saving contract:', error);
    }
  };

  const [isSending, setIsSending] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleSendContract = async () => {
    // Prevent multiple rapid clicks
    if (isSending) {
      return;
    }

    try {
      setIsSending(true);
      const formData = getValues();
      
      // First save the contract
      const contractData: ContractData = {
        id: generateContractId(),
        templateId: formData.templateId,
        sellerInfo: formData.sellerInfo,
        clientInfo: formData.clientInfo,
        contractFields: formData.contractFields,
        status: 'DRAFT' as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };


      // Save contract to database first
      const saveResponse = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: contractData.id,
          templateId: contractData.templateId,
          sellerInfo: contractData.sellerInfo,
          clientInfo: contractData.clientInfo,
          contractFields: contractData.contractFields,
          authorId: userId
        })
      });

      if (!saveResponse.ok) {
        const error = await saveResponse.json();
        alert(`Failed to save contract: ${error.error}`);
        return;
      }

      // Call the onSave callback to update UI
      onSave(contractData);

      // Then send it
      const response = await fetch(`/api/contracts/${contractData.id}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        const result = await response.json();
        const shareableLink = result.contract.shareableLink;
        
        // Copy link to clipboard
        const copied = await copyToClipboard(shareableLink);
        
        // Show success toast
        showToast({
          type: 'success',
          title: 'Contract Sent Successfully!',
          message: copied 
            ? `Contract link copied to clipboard: ${ensureFullUrl(shareableLink)}`
            : 'Contract has been sent to the client.',
          action: copied ? {
            label: 'View Contract',
            onClick: () => window.open(ensureFullUrl(shareableLink), '_blank')
          } : undefined,
          duration: 6000
        });
      } else {
        const error = await response.json();
        showToast({
          type: 'error',
          title: 'Failed to Send Contract',
          message: error.error || 'An error occurred while sending the contract.',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Error sending contract:', error);
      showToast({
        type: 'error',
        title: 'Failed to Send Contract',
        message: 'An unexpected error occurred. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleDownloadContract = async () => {
    if (isDownloading) {
      return;
    }

    try {
      setIsDownloading(true);
      const formData = getValues();
      
      // Generate contract content for PDF
      const contractContent = generateContractContent(formData);
      
      // Export to PDF
      await exportToPDF(contractContent, {
        format: 'pdf',
        filename: `contract-${formData.templateId || 'draft'}.pdf`,
        includeWatermark: false
      });

      showToast({
        type: 'success',
        title: 'Contract Downloaded',
        message: 'Contract has been downloaded as PDF',
        duration: 3000
      });
      
    } catch (error) {
      console.error('Error downloading contract:', error);
      showToast({
        type: 'error',
        title: 'Download Failed',
        message: 'Failed to download contract PDF. Please try again.',
        duration: 5000
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const generateContractContent = (formData: any) => {
    if (!template) {
      return '<div>Loading template...</div>';
    }

    // Use the processContractTemplate function to generate the actual contract content
    const processedTemplate = processContractTemplate(
      template,
      formData.sellerInfo,
      formData.clientInfo,
      formData.contractFields
    );

    // Convert markdown to HTML for better display
    const htmlContent = processedTemplate
      .replace(/^# (.*$)/gim, '<h1 style="text-align: center; margin-bottom: 20px; font-size: 16px; font-weight: bold; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; line-height: 1.2; max-width: 100%;">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 style="font-size: 14px; font-weight: bold; margin-bottom: 12px; margin-top: 20px; word-wrap: break-word; overflow-wrap: break-word;">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 style="font-size: 12px; font-weight: bold; margin-bottom: 8px; word-wrap: break-word; overflow-wrap: break-word;">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br/>');

    return `
      <div style="font-family: 'Times New Roman', serif; font-size: 12px; line-height: 1.4; color: #333; width: 100%; box-sizing: border-box; max-width: 100%; overflow: hidden;">
        ${htmlContent}
      </div>
    `;
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        backgroundColor: '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif'
      }}
    >
      {/* Header */}
      <div
        style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
          padding: '10px 16px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e5e7eb'
        }}
      >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Button
            variant="ghost"
            size="2"
            onClick={onBack}
            style={{
              boxShadow: 'none',
              outline: 'none',
              background: 'none',
              padding: '4px',
              minWidth: 'unset',
              minHeight: 'unset',
              borderRadius: '6px',
              transition: 'background 0.15s'
            }}
            onMouseOver={e => {
              e.currentTarget.style.background = '#f3f4f6';
            }}
            onMouseOut={e => {
              e.currentTarget.style.background = 'none';
            }}
            onFocus={e => {
              e.currentTarget.style.boxShadow = '0 0 0 2px #e5e7eb';
              e.currentTarget.style.background = '#f3f4f6';
            }}
            onBlur={e => {
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.background = 'none';
            }}
          >
            <ArrowLeft size={16} color="#6b7280" />
          </Button>
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <Text size="2" style={{ color: '#6b7280' }}>
              Contracts
            </Text>
            <Text size="2" style={{ color: '#6b7280' }}>
              /
            </Text>
            <Text size="2" style={{ color: '#374151' }}>
              Create Contract
            </Text>
          </div>
        </div>
        <Button
          color="blue"
          size="2"
          variant="classic"
          onClick={handleSendContract}
          disabled={!isFormValid || isSending}
          style={{
            backgroundColor: isFormValid ? '#3b82f6' : '#9ca3af',
            color: isFormValid ? 'white' : '#6b7280',
            borderColor: isFormValid ? '#3b82f6' : '#d1d5db',
            transition: 'all 0.2s ease-in-out'
          }}
        >
          {isSending ? 'Sending...' : 'Send contract'}
        </Button>
      </div>

      {/* Main Content */}
      <div style={{ display: 'flex', height: 'calc(100vh - 73px)' }}>
        {/* Left Panel - Form */}
        <div style={{
          width: '50%',
          backgroundColor: 'white',
          padding: '32px',
          overflowY: 'auto',
          borderRight: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {/* Customer Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Customer
              </Text>

              {/* Customer Input with Dropdown */}
              <div style={{ position: 'relative' }} data-customer-dropdown>
                <input
                  type="text"
                  placeholder="Find or add a customer"
                  value={customerSearch}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value);
                    setShowCustomerDropdown(true);
                  }}
                  onFocus={() => setShowCustomerDropdown(true)}
                    style={{
                      width: '100%',
                    height: '32px',
                    padding: '0 35px 0 10px',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    fontSize: '14px',
                    fontFamily: 'Inter Display, sans-serif',
                    fontWeight: '400',
                    lineHeight: '20px',
                    letterSpacing: '0.01em',
                    color: customerSearch ? '#202020' : 'rgba(0, 0, 0, 0.486275)',
                    outline: 'none',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                    boxSizing: 'border-box'
                  }}
                />
                <ChevronDown
                  size={16}
                  style={{
                    position: 'absolute',
                    right: '10px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#838383',
                    cursor: 'pointer'
                  }}
                  onClick={() => setShowCustomerDropdown(!showCustomerDropdown)}
                />

                {/* Customer Dropdown */}
                {showCustomerDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                      backgroundColor: 'white',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '200px',
                    overflowY: 'auto'
                  }}>

                    {/* Customer list */}
                    {loadingCustomers ? (
                      <div style={{ padding: '8px 12px', textAlign: 'center', color: '#6b7280' }}>
                        Loading customers...
                      </div>
                    ) : filteredCustomers.length === 0 ? (
                      <div style={{ padding: '8px 12px', textAlign: 'center', color: '#6b7280' }}>
                        No customers found. Fill out the client information below to add a new customer.
                      </div>
                    ) : (
                      filteredCustomers.map((customer, index) => (
                      <div
                        key={index}
                        style={{
                          padding: '8px 12px',
                          cursor: 'pointer',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                        onClick={() => {
                          setCustomerSearch(customer.name);
                          setShowCustomerDropdown(false);
                          // Populate form fields with selected customer
                          setValue('clientInfo.name', customer.name);
                          setValue('clientInfo.email', customer.email);
                          setValue('clientInfo.address', '123 Main St, City, State 12345'); // Default address
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                      >
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#202020',
                          fontWeight: '500'
                        }}>
                          {customer.name}
                        </Text>
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#6b7280'
                        }}>
                          {customer.email}
                        </Text>
                    </div>
                    ))
                    )}
                    </div>
                )}
                    </div>

                    {/* Client Information Form */}
                    <div style={{ marginBottom: '-5px', marginTop: '24px' }}>
                      <Text
                        size="2"
                        style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          fontSize: '14px',
                          lineHeight: '20px',
                          letterSpacing: '0.01em',
                          color: '#202020',
                          marginBottom: '8px',
                          display: 'block'
                        }}
                      >
                        Client Information
                      </Text>

                      {/* Client Name */}
                      <div style={{ marginBottom: '16px' }}>
                        <input
                          type="text"
                          placeholder="Client Name"
                          {...register('clientInfo.name')}
                          style={{
                            width: '100%',
                            height: '32px',
                            padding: '0 10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box'
                          }}
                        />
                        {errors.clientInfo?.name && (
                          <Text style={{ color: '#dc2626', fontSize: '12px', marginTop: '4px' }}>
                            {errors.clientInfo.name.message}
                          </Text>
                        )}
                      </div>

                      {/* Client Email */}
                      <div style={{ marginBottom: '16px' }}>
                        <input
                          type="email"
                          placeholder="Client Email"
                          {...register('clientInfo.email')}
                          style={{
                            width: '100%',
                            height: '32px',
                            padding: '0 10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box'
                          }}
                        />
                        {errors.clientInfo?.email && (
                          <Text style={{ color: '#dc2626', fontSize: '12px', marginTop: '4px' }}>
                            {errors.clientInfo.email.message}
                          </Text>
                        )}
                      </div>

                      {/* Client Address */}
                      <div style={{ marginBottom: '16px' }}>
                        <textarea
                          placeholder="Client Address"
                          {...register('clientInfo.address')}
                          style={{
                            width: '100%',
                            minHeight: '60px',
                            padding: '8px 10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box',
                            resize: 'vertical'
                          }}
                        />
                        {errors.clientInfo?.address && (
                          <Text style={{ color: '#dc2626', fontSize: '12px', marginTop: '4px' }}>
                            {errors.clientInfo.address.message}
                          </Text>
                        )}
                      </div>

                      {/* Save as Customer Button */}
                      <div style={{ marginBottom: '16px' }}>
                        <button
                          type="button"
                          onClick={async () => {
                            const clientName = watch('clientInfo.name');
                            const clientEmail = watch('clientInfo.email');
                            const clientAddress = watch('clientInfo.address');
                            
                            if (clientName && clientEmail) {
                              try {
                                await saveCustomer(clientName, clientEmail, clientAddress);
                                // Show success message or update UI
                                console.log('Customer saved successfully');
                              } catch (error) {
                                console.error('Error saving customer:', error);
                              }
                            }
                          }}
                          style={{
                            width: '100%',
                            height: '32px',
                            backgroundColor: '#f3f4f6',
                            border: '1px solid #d1d5db',
                            borderRadius: '8px',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '500',
                            color: '#374151',
                            cursor: 'pointer',
                            outline: 'none',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#e5e7eb';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#f3f4f6';
                          }}
                        >
                          <span style={{ fontSize: '16px' }}>+</span>
                          Save as Customer
                        </button>
                      </div>
                    </div>


                    </div>



            {/* Contract Template Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Contract Template
              </Text>

              {/* Template Dropdown */}
              <div style={{ position: 'relative' }} data-template-dropdown>
                <div
                  style={{
                    height: '32px',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    padding: '0 35px 0 10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    marginBottom: '8px',
                    cursor: 'pointer',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                  display: 'flex',
                  alignItems: 'center',
                    boxSizing: 'border-box'
                  }}
                  onClick={() => setShowTemplateDropdown(!showTemplateDropdown)}
                  onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                  onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                >
                  <Text
                    style={{
                      fontSize: '14px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '400',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: '#202020'
                    }}
                  >
                    {selectedTemplate?.name || 'Select Template'}
                  </Text>
                  <ChevronDown
                    size={16}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      color: '#838383'
                    }}
                  />
                </div>

                {/* Template Dropdown Menu */}
                {showTemplateDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'white',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '200px',
                    overflowY: 'auto'
                  }}>
                    {availableTemplates.map((template, index) => (
                      <div
                        key={template.id}
                        style={{
                          padding: '12px',
                          cursor: 'pointer',
                          borderBottom: index < availableTemplates.length - 1 ? '1px solid #f3f4f6' : 'none'
                        }}
                        onClick={() => {
                          setSelectedTemplate(template);
                          setTemplate(template); // Update the template state used for form fields
                          setShowTemplateDropdown(false);
                          setValue('templateId', template.id);
                          // Show preview when template is selected
                          setShowPreview(true);
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                      >
                        <div style={{ marginBottom: '4px' }}>
                          <Text style={{
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '500',
                            color: '#202020'
                          }}>
                            {template.name}
                          </Text>
                        </div>
                        <Text style={{
                          fontSize: '12px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#6b7280',
                          lineHeight: '16px'
                        }}>
                          {template.description}
                        </Text>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Due Date Section */}
            <div style={{ marginBottom: '24px', position: 'relative' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Due date
              </Text>

              {/* Due Date Dropdown */}
              <div style={{ position: 'relative' }} data-due-date-dropdown>
                <div
                    style={{
                    height: '32px',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    borderRadius: '8px',
                    padding: '0 35px 0 10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    marginBottom: '8px',
                    cursor: 'pointer',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                  display: 'flex',
                  alignItems: 'center',
                    boxSizing: 'border-box'
                  }}
                  onClick={() => setShowDueDateDropdown(!showDueDateDropdown)}
                  onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                  onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                >
                  <Text
                    style={{
                      fontSize: '14px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '400',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: '#202020'
                    }}
                  >
                    {selectedDueDate}
                  </Text>
                  <ChevronDown
                    size={16}
                    style={{
                      position: 'absolute',
                      right: '10px',
                      color: '#838383'
                    }}
                  />
                </div>

                {/* Due Date Dropdown Menu */}
                {showDueDateDropdown && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: '0',
                    right: '0',
                    backgroundColor: 'white',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    marginTop: '4px',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {/* Today */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                        display: 'flex',
                      justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                      onClick={() => {
                        setSelectedDueDate('Today');
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Today
                      </Text>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#6B7280',
                        fontWeight: '400'
                      }}>
                        {new Date().toLocaleDateString('en-US', {
                          month: '2-digit',
                          day: '2-digit',
                          year: 'numeric'
                        })}
                      </Text>
                    </div>

                    {/* Tomorrow */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                      onClick={() => {
                        const tomorrow = new Date();
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        setSelectedDueDate('Tomorrow');
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Tomorrow
                      </Text>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#6B7280',
                        fontWeight: '400'
                      }}>
                        {(() => {
                          const tomorrow = new Date();
                          tomorrow.setDate(tomorrow.getDate() + 1);
                          return tomorrow.toLocaleDateString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric'
                          });
                        })()}
                      </Text>
                    </div>

                    {/* Due in X days options */}
                    {[
                      { label: 'Due in 7 days', days: 7 },
                      { label: 'Due in 14 days', days: 14 },
                      { label: 'Due in 30 days', days: 30 }
                    ].map((option, index) => (
                      <div
                        key={index}
                        style={{
                          padding: '8px 12px',
                          cursor: 'pointer',
                          borderRadius: '6px',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                        onClick={() => {
                          setSelectedDueDate(option.label);
                          setShowDueDateDropdown(false);
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                      >
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#202020',
                          fontWeight: '400'
                        }}>
                          {option.label}
                        </Text>
                        <Text style={{
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          color: '#6B7280',
                          fontWeight: '400'
                        }}>
                          {(() => {
                            const futureDate = new Date();
                            futureDate.setDate(futureDate.getDate() + option.days);
                            return futureDate.toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric'
                            });
                          })()}
                        </Text>
                      </div>
                    ))}

                    {/* Separator */}
              <div style={{
                      height: '1px',
                      backgroundColor: '#E5E7EB',
                      margin: '8px 0'
                    }} />

                    {/* Custom Date Option */}
                    <div
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                        gap: '8px'
                      }}
                      onClick={() => {
                        setShowCalendar(true);
                        setShowDueDateDropdown(false);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F3F4F6'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        color: '#202020',
                        fontWeight: '400'
                      }}>
                        Custom date
                      </Text>
                </div>
              </div>
                )}
            </div>

              {/* Calendar Modal */}
              {showCalendar && (
                <div style={{
                  position: 'fixed',
                  top: '0',
                  left: '0',
                  right: '0',
                  bottom: '0',
                  backgroundColor: 'rgba(0, 0, 0, 0.5)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 2000
                }}>
                  <div style={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    padding: '24px',
                    width: '400px',
                    maxWidth: '90vw'
                  }}>
                    <Text style={{
                      fontSize: '16px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      color: '#202020',
                      marginBottom: '16px',
                      display: 'block'
                    }}>
                      Select Due Date
              </Text>

                    <div style={{ marginBottom: '16px' }}>
                      <FrostCalendar
                        onChange={(date) => {
                          if (date) {
                            // Convert CalendarDate to JavaScript Date
                            const jsDate = new Date(date.year, date.month - 1, date.day);
                            const formattedDate = jsDate.toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            });
                            setSelectedDueDate(`Due ${formattedDate}`);
                            setShowCalendar(false);
                          }
                        }}
                      />
                    </div>

                    <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                      <button
                        onClick={() => setShowCalendar(false)}
                    style={{
                          padding: '8px 16px',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          borderRadius: '6px',
                          backgroundColor: 'white',
                          fontSize: '14px',
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          color: '#6b7280',
                          cursor: 'pointer',
                          outline: 'none'
                        }}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}

            </div>

            {/* Description Section */}
            <div style={{ marginBottom: '24px' }}>
              <Text
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                Description
              </Text>
              <textarea
                placeholder="Add a description"
                style={{
                  width: '100%',
                  minHeight: '80px',
                  padding: '10px',
                  border: '1px solid rgba(0, 0, 0, 0.121569)',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  fontSize: '14px',
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '400',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: 'rgba(0, 0, 0, 0.486275)',
                  outline: 'none',
                  resize: 'vertical',
                  boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#3b82f6';
                  e.target.style.color = '#202020';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'rgba(0, 0, 0, 0.121569)';
                  if (!e.target.value) e.target.style.color = 'rgba(0, 0, 0, 0.486275)';
                }}
              />
            </div>

            {/* Payment Toggle Section */}
            <div style={{ marginBottom: '24px' }}>
              <Text
                as="label"
                size="2"
                style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '500',
                  fontSize: '14px',
                  lineHeight: '20px',
                  letterSpacing: '0.01em',
                  color: '#202020',
                  cursor: 'pointer'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Switch
                    color="blue"
                    checked={enablePayments}
                    onCheckedChange={setEnablePayments}
                    size="1"
                  />
                  Enable payment collection
                </div>
              </Text>
            </div>

            {/* Price Section */}
            {enablePayments && (
            <div style={{
              boxSizing: 'border-box',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              padding: '20px',
              gap: '20px',
              width: '100%',
              backgroundColor: '#FFFFFF',
              border: '1px solid #E8E8E8',
              boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              {/* Price Header */}
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'flex-start',
                padding: '0px',
                gap: '16px',
                width: '100%',
                height: '24px'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0px',
                  gap: '16px',
                  width: '100%',
                  height: '24px'
                }}>
                  <Text
                    style={{
                      fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                      fontWeight: '500',
                      fontSize: '20px',
                      lineHeight: '28px',
                      letterSpacing: '0.01em',
                      color: '#202020',
                      height: '28px',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {selectedPricing === 'free' ? 'Free' :
                     selectedPricing === 'one-time' ? `$${contractValue} / One-time` :
                     `$${contractValue} / ${recurringPeriod.charAt(0).toUpperCase() + recurringPeriod.slice(1)}`}
                  </Text>
                  <div style={{ width: '24px', height: '24px', transform: 'matrix(1, 0, 0, -1, 0, 0)' }}>
                    <ChevronDown size={16} style={{ color: 'rgba(0, 0, 0, 0.486275)' }} />
                  </div>
                </div>
              </div>

              {/* Price Type Buttons */}
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'flex-end',
                padding: '0px',
                width: '100%',
                height: '32px'
              }}>
                {/* Free Button */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  isolation: 'isolate',
                  width: '33.33%',
                  height: '32px'
                }}>
                  <button
                    onClick={() => setSelectedPricing('free')}
                  style={{
                      boxSizing: 'border-box',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0px 12px',
                      gap: '8px',
                      width: '100%',
                      height: '32px',
                      backgroundColor: selectedPricing === 'free' ? '#EBF2FF' : '#FFFFFF',
                      borderTop: selectedPricing === 'free' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderBottom: selectedPricing === 'free' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderLeft: selectedPricing === 'free' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderRight: 'none',
                      boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                      borderRadius: '8px 0px 0px 8px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: selectedPricing === 'free' ? '#265CCF' : '#202020',
                      cursor: 'pointer',
                      outline: 'none'
                  }}
                >
                  Free
                  </button>
                </div>

                {/* One-time Button */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  isolation: 'isolate',
                  width: '33.33%',
                  height: '32px'
                }}>
                  <button
                    onClick={() => setSelectedPricing('one-time')}
                  style={{
                      boxSizing: 'border-box',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0px 12px',
                      gap: '8px',
                      width: '100%',
                      height: '32px',
                      backgroundColor: selectedPricing === 'one-time' ? '#EBF2FF' : '#FFFFFF',
                      borderTop: selectedPricing === 'one-time' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderBottom: selectedPricing === 'one-time' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderLeft: 'none',
                      borderRight: 'none',
                      boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                      borderRadius: '0px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: selectedPricing === 'one-time' ? '#265CCF' : '#202020',
                      cursor: 'pointer',
                      outline: 'none'
                  }}
                >
                  One-time
                  </button>
                </div>

                {/* Recurring Button */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  isolation: 'isolate',
                  width: '33.33%',
                  height: '32px'
                }}>
                  <button
                    onClick={() => setSelectedPricing('recurring')}
                  style={{
                      boxSizing: 'border-box',
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0px 12px',
                      gap: '8px',
                      width: '100%',
                      height: '32px',
                      backgroundColor: selectedPricing === 'recurring' ? '#EBF2FF' : '#FFFFFF',
                      borderTop: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderBottom: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderRight: selectedPricing === 'recurring' ? '1px solid #7EA7F5' : '1px solid rgba(0, 0, 0, 0.121569)',
                      borderLeft: 'none',
                      boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                      borderRadius: '0px 8px 8px 0px',
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      letterSpacing: '0.01em',
                      color: selectedPricing === 'recurring' ? '#265CCF' : '#202020',
                      cursor: 'pointer',
                      outline: 'none'
                  }}
                >
                  Recurring
                  </button>
                </div>
              </div>



              {/* Price Input Section */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                padding: '0px',
                gap: '32px',
                width: '100%'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  padding: '0px',
                  gap: '16px',
                  width: '100%'
                }}>
                  {/* Price Input Row */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-end',
                    padding: '0px',
                    gap: '12px',
                    width: '100%',
                    height: '60px'
                  }}>
                    {/* Price Input with Label */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      padding: '0px',
                      gap: '8px',
                      flex: '1',
                      height: '60px'
                    }}>
                      <Text
                        style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontWeight: '500',
                          fontSize: '14px',
                          lineHeight: '20px',
                          letterSpacing: '0.01em',
                          color: '#202020',
                          height: '20px'
                        }}
                      >
                    Price:
                  </Text>
                  <div style={{
                    position: 'relative',
                    width: '100%',
                    height: '32px',
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    border: '1px solid rgba(0, 0, 0, 0.121569)',
                    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                    borderRadius: '8px'
                  }}>
                    <span style={{
                      paddingLeft: '10px',
                      fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                      fontWeight: '500',
                      fontSize: '14px',
                      color: '#202020'
                    }}>
                      $
                    </span>
                  <input
                    type="text"
                      value={contractValue}
                      onChange={(e) => setContractValue(e.target.value)}
                      placeholder="100"
                    style={{
                        border: 'none',
                        outline: 'none',
                        backgroundColor: 'transparent',
                        fontFamily: '"Acid Grotesk", "Inter", sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        color: '#202020',
                        padding: '0 4px',
                        flex: selectedPricing === 'recurring' ? '0 0 auto' : '1',
                        width: selectedPricing === 'recurring' ? '60px' : 'auto'
                      }}
                    />
                    {selectedPricing === 'recurring' && (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer',
                          padding: '0 8px 0 4px',
                          flex: '1',
                          justifyContent: 'flex-end'
                        }}
                        onClick={() => setShowRecurringDropdown(!showRecurringDropdown)}
                        data-recurring-dropdown
                      >
                        <span style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontSize: '14px',
                          color: '#6b7280',
                          marginRight: '4px'
                        }}>
                          /
                        </span>
                        <span style={{
                          fontFamily: 'Inter Display, sans-serif',
                          fontSize: '14px',
                          color: '#202020',
                          marginRight: '4px'
                        }}>
                          1 {recurringPeriod === 'monthly' ? 'month' : recurringPeriod === 'yearly' ? 'year' : 'week'}
                        </span>
                        <ChevronDown size={12} style={{ color: '#6b7280' }} />
                        {/* Recurring Period Dropdown */}
                        {showRecurringDropdown && (
                          <div style={{
                            position: 'absolute',
                            top: '100%',
                            right: '0',
                            backgroundColor: 'white',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                            zIndex: 1000,
                            marginTop: '4px',
                            minWidth: '120px'
                          }}>
                            {[
                              { value: 'weekly', label: '1 week' },
                              { value: 'monthly', label: '1 month' },
                              { value: 'yearly', label: '1 year' }
                            ].map((period, index) => (
                               <div
                                 key={index}
                                 style={{
                                   padding: '4px 12px',
                                   cursor: 'pointer',
                                   borderBottom: index < 2 ? '1px solid #f3f4f6' : 'none'
                                 }}
                                onClick={() => {
                                  setRecurringPeriod(period.value as 'weekly' | 'monthly' | 'yearly');
                                  setShowRecurringDropdown(false);
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                              >
                                <Text style={{
                                  fontSize: '14px',
                                  fontFamily: 'Inter Display, sans-serif',
                                  color: '#202020'
                                }}>
                                  {period.label}
                  </Text>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                    {/* Currency Dropdown */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      padding: '0px',
                      gap: '8px',
                      width: '74px',
                      height: '32px',
                      position: 'relative'
                    }} data-currency-dropdown>
                      <div
                        style={{
                          boxSizing: 'border-box',
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          padding: '0px',
                          isolation: 'isolate',
                          width: '74px',
                          height: '32px',
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                          borderRadius: '8px',
                          cursor: 'pointer'
                        }}
                        onClick={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
                        onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                        onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                      >
                        <div style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          padding: '0px 10px',
                          gap: '6px',
                          width: '74px',
                          height: '32px'
                        }}>
                          <Text style={{
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            fontSize: '14px',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: 'rgba(0, 0, 0, 0.87451)',
                            flex: '1'
                          }}>
                            {selectedCurrency}
                          </Text>
                          <ChevronDown size={16} style={{ color: '#838383' }} />
                </div>
              </div>

                      {/* Currency Dropdown Menu */}
                      {showCurrencyDropdown && (
                        <div style={{
                          position: 'absolute',
                          top: '100%',
                          left: '0',
                          right: '0',
                          backgroundColor: 'white',
                          border: '1px solid rgba(0, 0, 0, 0.121569)',
                          borderRadius: '8px',
                          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
                          zIndex: 1000,
                          marginTop: '4px',
                          maxHeight: '200px',
                          overflowY: 'auto'
                        }}>
                          {['USD', 'EUR', 'GBP', 'CAD', 'AUD'].map((currency, index) => (
                            <div
                              key={index}
                              style={{
                                padding: '8px 12px',
                                cursor: 'pointer',
                                borderBottom: index < 4 ? '1px solid #f3f4f6' : 'none'
                              }}
                              onClick={() => {
                                setSelectedCurrency(currency);
                                setShowCurrencyDropdown(false);
                              }}
                              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
                            >
                              <Text style={{
                                fontSize: '14px',
                                fontFamily: 'Inter Display, sans-serif',
                                color: '#202020'
                              }}>
                                {currency}
                              </Text>
              </div>
                          ))}
            </div>
                      )}


                </div>
              </div>

                  {/* Auto Price Buttons */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: '0px',
                    gap: '12px',
                    width: '100%'
                  }}>
                    {/* $9.99 Button */}
                    <button
                      onClick={() => setContractValue('9.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $9.99
                      </Text>
                    </button>

                    {/* $49.99 Button */}
                    <button
                      onClick={() => setContractValue('49.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $49.99
                      </Text>
                    </button>

                    {/* $149.99 Button */}
                    <button
                      onClick={() => setContractValue('149.99')}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '4px 8px',
                        gap: '4px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(0, 0, 0, 0.121569)',
                        borderRadius: '8px',
                        boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                        cursor: 'pointer',
                        outline: 'none'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.borderColor = '#9ca3af'}
                      onMouseLeave={(e) => e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.121569)'}
                    >
                      <span style={{
                        fontSize: '16px',
                        color: '#6B7280',
                        fontWeight: 'normal'
                      }}>
                        +
                      </span>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#202020'
                      }}>
                        $149.99
                      </Text>
                    </button>

                    {/* More Button */}
                    <div style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: '8px',
                      marginLeft: 'auto'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 12L8 4M4 8L12 8" stroke="#3B82F6" strokeWidth="1.5" strokeLinecap="round"/>
                      </svg>
                      <Text style={{
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.01em',
                        color: '#3B82F6'
                      }}>
                        More
                      </Text>
                    </div>
                  </div>
              </div>
              </div>

            </div>
            )}

            {/* Contract Fields Section */}
            {template && template.fields && template.fields.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Text
                  size="2"
                  style={{
                    fontFamily: 'Inter Display, sans-serif',
                    fontWeight: '500',
                    fontSize: '14px',
                    lineHeight: '20px',
                    letterSpacing: '0.01em',
                    color: '#202020',
                    marginBottom: '16px',
                    display: 'block'
                  }}
                >
                  Contract Details
                </Text>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {template?.fields?.map((field: any) => (
                    <div key={field.id || `field-${Math.random()}`} style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <Text style={{
                        fontSize: '14px',
                        fontFamily: 'Inter Display, sans-serif',
                        fontWeight: '500',
                        color: '#374151'
                      }}>
                        {field.label} {field.required && <span style={{ color: '#dc2626' }}>*</span>}
                      </Text>
                      {field.type === 'textarea' ? (
                        <textarea
                          placeholder={field.placeholder}
                          {...register(`contractFields.${field.id}`)}
                          required={field.required}
                          style={{
                            width: '100%',
                            minHeight: '80px',
                            padding: '10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box',
                            resize: 'vertical'
                          }}
                        />
                      ) : (
                        <input
                          type={field.type === 'date' ? 'date' : field.type === 'number' ? 'number' : field.type === 'email' ? 'email' : 'text'}
                          placeholder={field.placeholder}
                          {...register(`contractFields.${field.id}`)}
                          required={field.required}
                          style={{
                            width: '100%',
                            height: '40px',
                            padding: '0 10px',
                            border: '1px solid rgba(0, 0, 0, 0.121569)',
                            borderRadius: '8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '14px',
                            fontFamily: 'Inter Display, sans-serif',
                            fontWeight: '400',
                            lineHeight: '20px',
                            letterSpacing: '0.01em',
                            color: '#202020',
                            outline: 'none',
                            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.06)',
                            boxSizing: 'border-box'
                          }}
                        />
                      )}
                      {errors.contractFields?.[field.id] && (
                        <Text style={{ color: '#dc2626', fontSize: '12px', marginTop: '4px' }}>
                          {String(errors.contractFields[field.id]?.message || 'This field is required')}
                        </Text>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

          </div>
        </div>


        {/* Right Panel - Preview */}
        <div style={{
          width: '50%',
          backgroundColor: '#f8f9fa',
          padding: '24px',
          overflowY: 'auto'
        }}>

         
          {/* Preview Content - Full Width Document */}
          <div style={{ display: 'flex', justifyContent: 'center', padding: '0 10px', overflow: 'hidden', height: '100%' }}>
            <div style={{ width: '100%', maxWidth: '600px', overflow: 'auto', height: '100%' }}>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                  {/* Contract Document */}
                  <div style={{
                    padding: '32px 40px',
                    backgroundColor: 'white',
                    fontFamily: 'Inter Display, sans-serif',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb',
                    overflow: 'hidden',
                    wordWrap: 'break-word',
                    overflowWrap: 'break-word',
                    maxWidth: '100%',
                    boxSizing: 'border-box'
                  }}>
                    <div 
                      dangerouslySetInnerHTML={{ 
                        __html: generateContractContent(watch())
                      }}
                    />
                </div>

                  {/* Action Buttons - Outside Document */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                    marginTop: '24px',
                    padding: '0 56px'
                  }}>
                    <Button 
                      variant="surface" 
                      size="2" 
                      style={{ width: '100%' }}
                      onClick={handleDownloadContract}
                      disabled={isDownloading}
                    >
                      <Download size={16} />
                      {isDownloading ? 'Downloading...' : 'Download contract'}
                    </Button>

                    <Button
                      color="blue"
                      size="2"
                      variant="classic"
                      style={{ 
                        width: '100%',
                        backgroundColor: isFormValid ? '#3b82f6' : '#9ca3af',
                        color: isFormValid ? 'white' : '#6b7280',
                        borderColor: isFormValid ? '#3b82f6' : '#d1d5db',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onClick={handleSendContract}
                      disabled={!isFormValid || isSending}
                    >
                      {isSending ? 'Sending...' : 'Send contract'}
                    </Button>
              </div>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
