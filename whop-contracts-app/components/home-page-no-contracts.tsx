'use client';

import { Button, Text, Heading } from 'frosted-ui';

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface Company {
  id: string;
  title: string;
}

interface HomePageNoContractsProps {
  user: User;
  company: Company;
  onCreateContract: () => void;
}

export function HomePageNoContracts({ user, company, onCreateContract }: HomePageNoContractsProps) {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Inter Display, sans-serif',
      padding: '24px'
    }}>
      {/* Main Content Area - No Contracts State */}
      <div
        style={{
          textAlign: 'center',
          maxWidth: '480px',
          margin: '0 auto',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        {/* Icon */}
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>
          <img
            src="/Frame 2085676294.png"
            alt="No contracts"
            style={{
              width: '140px',
              height: '140px',
              objectFit: 'contain'
            }}
          />
        </div>

        <Heading
          size="8"
          style={{
            fontFamily: 'Inter Display, sans-serif',
            fontWeight: '600',
            color: '#111827',
            fontSize: '28px',
            lineHeight: '36px',
            letterSpacing: '-0.02em',
            margin: '0 0 20px 0'
          }}
        >
          No contracts yet
        </Heading>

        <Text
          size="4"
          style={{
            fontFamily: 'Inter Display, sans-serif',
            color: '#6b7280',
            fontSize: '16px',
            lineHeight: '24px',
            maxWidth: '360px',
            margin: '0 0 32px 0'
          }}
        >
          Get started by creating your first contract
        </Text>

        {/* Create Contract Button */}
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            color="blue"
            size="2"
            variant="classic"
            onClick={onCreateContract}
          >
            Create contract
          </Button>
        </div>
      </div>
    </div>
  );
}