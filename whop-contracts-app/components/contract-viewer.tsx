'use client';

import { useState, useEffect } from 'react';
import { ContractData } from '../lib/types';
import { DigitalSignature } from './digital-signature';
import { Download, FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { getTemplateById } from '../lib/contract-templates';
import { processContractTemplate } from '../lib/contract-utils';
import { Button, Text, Checkbox } from 'frosted-ui';

interface ContractViewerProps {
  contractId: string;
  onContractSigned: (signedContractId: string, signatureData: string) => void;
  onDownloadPDF: (contractId: string) => void;
}

export function ContractViewer({ contractId, onContractSigned, onDownloadPDF }: ContractViewerProps) {
  const [contract, setContract] = useState<ContractData | null>(null);
  const [contractContent, setContractContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSigning, setIsSigning] = useState(false);

  const [termsAccepted, setTermsAccepted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [signatureData, setSignatureData] = useState<string | null>(null);

  useEffect(() => {
    fetchContract();
  }, [contractId]);

  const fetchContract = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/contracts/${contractId}`);
      if (!response.ok) {
        throw new Error('Contract not found');
      }
      
      const data = await response.json();
      const contractData = data.contract;
      setContract(contractData);
      
      // Generate the full contract content
      const template = await getTemplateById(contractData.templateId);
      if (template) {
        const processedContent = processContractTemplate(
          template,
          contractData.sellerInfo,
          contractData.clientInfo,
          contractData.contractFields
        );
        setContractContent(processedContent);
      } else {
        setError('Template not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load contract');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignatureComplete = async (signatureData: string) => {
    if (!contract || !termsAccepted) return;

    try {
      setIsSigning(true);
      
      const response = await fetch(`/api/contracts/${contractId}/sign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureData,
          termsAccepted,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save signature');
      }

      const result = await response.json();
      
      // Update contract status locally
      setContract(prev => prev ? { ...prev, status: 'SIGNED', clientSignature: signatureData, clientSignatureAt: new Date() } : null);
      
      // Call the callback
      onContractSigned(contractId, signatureData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save signature');
    } finally {
      setIsSigning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SIGNED':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'DRAFT':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'SIGNED':
        return 'Signed';
      case 'COMPLETED':
        return 'Completed';
      case 'DRAFT':
        return 'Draft';
      default:
        return status;
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto'
          }}></div>
          <p style={{
            marginTop: '16px',
            color: '#6b7280',
            fontFamily: 'Inter Display, sans-serif',
            fontSize: '14px'
          }}>Loading contract...</p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: '#f8f9fa',
        fontFamily: 'Inter Display, sans-serif',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <AlertCircle style={{
            width: '48px',
            height: '48px',
            color: '#dc2626',
            margin: '0 auto 16px'
          }} />
          <h2 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '8px',
            fontFamily: 'Inter Display, sans-serif'
          }}>Contract Not Found</h2>
          <p style={{
            color: '#6b7280',
            marginBottom: '16px',
            fontFamily: 'Inter Display, sans-serif',
            fontSize: '14px'
          }}>{error || 'The requested contract could not be found.'}</p>
          <button
            onClick={() => window.history.back()}
            style={{
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '8px',
              border: 'none',
              fontFamily: 'Inter Display, sans-serif',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }


  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      fontFamily: 'Inter Display, sans-serif',
      padding: '24px'
    }}>
      <div style={{
        maxWidth: '1024px',
        margin: '0 auto'
      }}>
        {/* Contract Document */}
        <div style={{
          backgroundColor: 'white',
          border: '1px solid rgba(0, 0, 0, 0.121569)',
          borderRadius: '8px',
          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
          padding: '32px',
          marginBottom: '24px'
        }}>
          <div style={{
            color: '#374151',
            whiteSpace: 'pre-wrap',
            fontFamily: 'Inter Display, sans-serif',
            fontSize: '16px',
            lineHeight: '1.6'
          }}>
            {contractContent}
          </div>
        </div>

        {/* Signature Area */}
        {(contract.status === 'DRAFT' || contract.status === 'SENT') && !contract.clientSignature && (
          <div style={{
            backgroundColor: 'white',
            border: '1px solid rgba(0, 0, 0, 0.121569)',
            borderRadius: '8px',
            boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
            padding: '32px',
            marginBottom: '24px'
          }}>
            <h2 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: '#111827',
              marginBottom: '24px',
              textAlign: 'center',
              fontFamily: 'Inter Display, sans-serif'
            }}>Sign Contract</h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', alignItems: 'center' }}>
              <div style={{
                border: '2px dashed #d1d5db',
                borderRadius: '8px',
                padding: '24px',
                width: '100%',
                maxWidth: '600px'
              }}>
                <DigitalSignature
                  onSignatureComplete={(data) => setSignatureData(data)}
                  onClear={() => setSignatureData(null)}
                  width={600}
                  height={200}
                />
              </div>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <Text
                  as="label"
                  size="4"
                  style={{
                    fontFamily: 'Inter Display, sans-serif',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <Checkbox
                      checked={termsAccepted}
                      onCheckedChange={(checked) => setTermsAccepted(checked === true)}
                      size="2"
                    />
                    I agree to the terms and conditions of this contract
                  </div>
                </Text>
              </div>

              <Button
                onClick={() => {
                  if (signatureData && termsAccepted && !isSigning) {
                    handleSignatureComplete(signatureData);
                  }
                }}
                disabled={!termsAccepted || !signatureData || isSigning}
                color="green"
                size="3"
                variant="classic"
                style={{ minWidth: '200px' }}
              >
                {isSigning ? 'Signing...' : 'Sign Contract'}
              </Button>
            </div>
          </div>
        )}

        {/* Already Signed Message */}
        {contract.clientSignature && (
          <div style={{
            backgroundColor: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '8px',
            padding: '24px',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '12px',
              marginBottom: '8px'
            }}>
              <CheckCircle style={{ width: '24px', height: '24px', color: '#0ea5e9' }} />
              <h2 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#0c4a6e',
                fontFamily: 'Inter Display, sans-serif'
              }}>Contract Signed</h2>
            </div>
            <p style={{
              color: '#0c4a6e',
              fontFamily: 'Inter Display, sans-serif',
              fontSize: '16px',
              margin: 0
            }}>
              This contract was signed on {contract.clientSignatureAt ? formatDate(contract.clientSignatureAt) : 'Unknown date'}.
            </p>
          </div>
        )}

        {/* Download Button */}
        <div style={{
          backgroundColor: 'white',
          border: '1px solid rgba(0, 0, 0, 0.121569)',
          borderRadius: '8px',
          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
          padding: '16px',
          textAlign: 'center'
        }}>
          <Button
            onClick={() => onDownloadPDF(contractId)}
            color="indigo"
            size="2"
            variant="classic"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <Download style={{ width: '16px', height: '16px' }} />
            Download PDF
          </Button>
        </div>
      </div>
    </div>
  );
}
