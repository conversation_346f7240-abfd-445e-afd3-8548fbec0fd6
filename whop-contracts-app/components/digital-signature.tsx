'use client';

import { useRef, useEffect, useState } from 'react';
import { Heading, Text, Button } from 'frosted-ui';

interface DigitalSignatureProps {
  onSignatureComplete: (signatureData: string) => void;
  onClear: () => void;
  width?: number;
  height?: number;
}

export function DigitalSignature({ 
  onSignatureComplete, 
  onClear, 
  width = 400, 
  height = 200 
}: DigitalSignatureProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up canvas
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Set canvas size
    canvas.width = width * window.devicePixelRatio;
    canvas.height = height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
  }, [width, height]);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;
    setIsDrawing(false);
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Check if there's actual content drawn
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // More robust signature detection
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    let hasContent = false;
    
    // Check for any non-transparent pixels (more lenient detection)
    for (let i = 0; i < imageData.data.length; i += 4) {
      const alpha = imageData.data[i + 3]; // Alpha channel
      if (alpha > 0) {
        hasContent = true;
        break;
      }
    }

    if (hasContent) {
      setHasSignature(true);
      const signatureData = canvas.toDataURL('image/png');
      onSignatureComplete(signatureData);
    }
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
    onClear();
  };

  return (
    <div className="signature-container">
      <div className="signature-header">
        <Heading size="5" weight="semi-bold" className="text-black mb-2">
          Digital Signature
        </Heading>
        <Text size="3" className="text-black mb-4">
          Please sign in the box below using your mouse or touch device
        </Text>
      </div>
      
      <div className="signature-pad-container border border-gray-200 rounded-lg overflow-hidden bg-white">
        <canvas
          ref={canvasRef}
          className="signature-pad cursor-crosshair bg-white"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
        />
      </div>
      
      <div className="signature-actions mt-4 flex justify-between items-center">
        <Button
          variant="ghost"
          onClick={clearSignature}
          disabled={!hasSignature}
          className="text-black"
        >
          Clear Signature
        </Button>
        
        <div className="signature-status">
          {hasSignature ? (
            <Text size="3" className="text-green-600 font-medium flex items-center gap-1">
              ✓ Signature captured
            </Text>
          ) : (
            <Text size="3" className="text-gray-500">
              Please sign above
            </Text>
          )}
        </div>
      </div>

      <style jsx>{`
        .signature-pad {
          display: block;
          touch-action: none;
          width: 100%;
          height: 100%;
        }
        
        .signature-pad:active {
          cursor: crosshair;
        }
        
        .signature-container {
          background: #FFFFFF;
          border-radius: 8px;
          padding: 0;
        }
        
        .signature-header {
          margin-bottom: 16px;
        }
        
        .signature-pad-container {
          box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.06);
        }
      `}</style>
    </div>
  );
}
