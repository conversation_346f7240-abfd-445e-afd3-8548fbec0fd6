'use client';

import { useState, useEffect } from 'react';
import { Sun, Moon } from 'lucide-react';
import { Button } from 'frosted-ui';
import { useThemeContext } from 'frosted-ui';
import { useTheme } from './theme-provider';

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const themeContext = useThemeContext();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Sync our theme state with frosted-ui's theme context
  useEffect(() => {
    if (mounted && themeContext.appearance !== theme) {
      themeContext.onAppearanceChange(theme);
    }
  }, [theme, themeContext, mounted]);

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="2"
        className="ml-2"
        disabled
      >
        <div className="h-4 w-4" />
      </Button>
    );
  }

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  return (
    <Button
      variant="ghost"
      size="2"
      onClick={toggleTheme}
      className="ml-2"
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </Button>
  );
}