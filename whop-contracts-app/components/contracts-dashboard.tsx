'use client';

import { useState, useEffect } from 'react';
import { ContractBuilderNew as ContractBuilder } from './contract-builder-new';
import { NotificationsPanel } from './notifications-panel';
import { HomePageNoContracts } from './home-page-no-contracts';
import { ContractData } from '@/lib/types';
import { notificationService } from '@/lib/notifications';
import { initializeDemoNotifications, simulateContractActivity } from '@/lib/demo-data';
import { formatDate } from '@/lib/contract-utils';
import { showToast } from './toast';
import { Plus, FileText, Settings, Users } from 'lucide-react';
import { Heading, Text, Button, Badge } from 'frosted-ui';
import { ThemeToggle } from './theme-toggle';

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface Company {
  id: string;
  title: string;
}

interface ContractsDashboardProps {
  user: User;
  company: Company;
  companyId: string;
  userId: string;
}

type View = 'overview' | 'builder' | 'home';

export function ContractsDashboard({ user, company, companyId, userId }: ContractsDashboardProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [contracts, setContracts] = useState<ContractData[]>([]);
  const [manualView, setManualView] = useState<View | null>(null);
  const [isLoadingContracts, setIsLoadingContracts] = useState(true);

  // Initialize demo notifications and load contracts on component mount
  useEffect(() => {
    initializeDemoNotifications();
    loadContracts();
  }, []);

  const loadContracts = async () => {
    try {
      setIsLoadingContracts(true);
      const response = await fetch(`/api/contracts?authorId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setContracts(data.contracts || []);
      } else {
        console.error('Failed to load contracts');
      }
    } catch (error) {
      console.error('Error loading contracts:', error);
    } finally {
      setIsLoadingContracts(false);
    }
  };

  // Determine current view: if no contracts exist and no manual view is set, show home page
  // Otherwise, use manual view or default to overview
  const currentView: View = manualView || (isLoadingContracts ? 'overview' : (contracts.length === 0 ? 'home' : 'overview'));

  const handleTemplateSelect = (templateId: string) => {
    console.log('Template selected:', templateId);
    setSelectedTemplateId(templateId);
    setManualView('builder');
  };

  const handleContractSave = async (contract: ContractData) => {
    try {

      // Validate required fields before sending
      if (!contract.templateId) {
        alert('Template ID is missing. Please select a template.');
        return;
      }
      if (!contract.sellerInfo || !contract.sellerInfo.name || !contract.sellerInfo.email) {
        alert('Seller information is missing. Please fill out seller details.');
        return;
      }
      if (!contract.clientInfo || !contract.clientInfo.name || !contract.clientInfo.email) {
        alert('Client information is missing. Please fill out client details.');
        return;
      }
      if (!userId) {
        alert('User ID is missing. Please refresh the page and try again.');
        return;
      }

      // Save contract to database
      const response = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: contract.id,
          templateId: contract.templateId,
          sellerInfo: contract.sellerInfo,
          clientInfo: contract.clientInfo,
          contractFields: contract.contractFields || {},
          authorId: userId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Failed to save contract:', error);
        showToast({
          type: 'error',
          title: 'Failed to Save Contract',
          message: error.error || 'An error occurred while saving the contract.',
          duration: 5000
        });
        return;
      }

      // Refresh contracts from database
      loadContracts();
      
      // Add notification for contract creation
      notificationService.addNotification({
        type: 'contract_viewed',
        title: 'Contract Created',
        message: `Contract for ${contract.clientInfo.name} has been created successfully`,
        contractId: contract.id,
        clientEmail: contract.clientInfo.email,
      });

      // Simulate contract activity for demo purposes
      simulateContractActivity(contract.id, contract.clientInfo.email);
      
      setManualView('overview');
    } catch (error) {
      console.error('Error saving contract:', error);
      showToast({
        type: 'error',
        title: 'Failed to Save Contract',
        message: 'An unexpected error occurred. Please try again.',
        duration: 5000
      });
    }
  };

  const renderContent = () => {
    console.log('Current view:', currentView, 'Selected template ID:', selectedTemplateId);
    switch (currentView) {
      case 'home':
        return (
          <HomePageNoContracts
            user={user}
            company={company}
            onCreateContract={() => setManualView('builder')}
          />
        );
      case 'builder':
        return (
          <ContractBuilder
            templateId={selectedTemplateId}
            user={user}
            userId={userId}
            companyId={companyId}
            onSave={handleContractSave}
            onBack={() => setManualView(contracts.length > 0 ? 'overview' : 'home')}
          />
        );
      default:
        return (
          <div style={{
            minHeight: '100vh',
            backgroundColor: '#f8f9fa',
            fontFamily: 'Inter Display, sans-serif',
            padding: '24px'
          }}>
            {/* Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '32px'
            }}>
              <div>
                <Heading
                  size="8"
                  style={{
                    fontFamily: 'Inter Display, sans-serif',
                    fontWeight: '600',
                    color: '#111827',
                    fontSize: '32px',
                    lineHeight: '40px',
                    letterSpacing: '-0.02em',
                    margin: 0
                  }}
                >
                  Contracts
                </Heading>
              </div>

              <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                <Button
                  color="blue"
                  size="2"
                  variant="classic"
                  onClick={() => setManualView('builder')}
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <Plus size={16} />
                  Create Contract
                </Button>

              </div>

            </div>

            {/* Overview Stats */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '24px',
              marginBottom: '32px'
            }}>
              <div style={{
                padding: '24px',
                backgroundColor: 'white',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <Text style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  marginBottom: '8px'
                }}>
                  Total Contracts
                </Text>
                <Heading size="6" style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '600',
                  color: '#111827',
                  margin: 0
                }}>
                  {contracts.length}
                </Heading>
              </div>

              <div style={{
                padding: '24px',
                backgroundColor: 'white',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <Text style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  marginBottom: '8px'
                }}>
                  Active
                </Text>
                <Heading size="6" style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '600',
                  color: '#111827',
                  margin: 0
                }}>
                  {contracts.filter(c => c.status === 'SIGNED' || c.status === 'SENT').length}
                </Heading>
              </div>

              <div style={{
                padding: '24px',
                backgroundColor: 'white',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <Text style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  marginBottom: '8px'
                }}>
                  Drafts
                </Text>
                <Heading size="6" style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '600',
                  color: '#111827',
                  margin: 0
                }}>
                  {contracts.filter(c => c.status === 'DRAFT').length}
                </Heading>
              </div>

              <div style={{
                padding: '24px',
                backgroundColor: 'white',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)'
              }}>
                <Text style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#6b7280',
                  marginBottom: '8px'
                }}>
                  Total Value
                </Text>
                <Heading size="6" style={{
                  fontFamily: 'Inter Display, sans-serif',
                  fontWeight: '600',
                  color: '#111827',
                  margin: 0
                }}>
                  ${contracts.reduce((sum, c) => sum + (parseFloat(c.contractFields?.amount) || 0), 0).toFixed(2)}
                </Heading>
              </div>
            </div>

            {/* Recent Contracts */}
            {contracts.length > 0 && (
              <div style={{
                backgroundColor: 'white',
                border: '1px solid rgba(0, 0, 0, 0.121569)',
                borderRadius: '8px',
                boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.05)',
                overflow: 'hidden'
              }}>
                <div style={{
                  padding: '24px 24px 0 24px',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.121569)'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '16px'
                  }}>
                    <Heading size="5" style={{
                      fontFamily: 'Inter Display, sans-serif',
                      fontWeight: '600',
                      color: '#111827',
                      margin: 0
                    }}>
                      Recent Contracts
                    </Heading>
                  </div>
                </div>

                {/* Table Header */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 120px 150px 120px 120px 80px',
                  gap: '16px',
                  padding: '16px 24px',
                  backgroundColor: '#f8f9fa',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.121569)'
                }}>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Contract ID
                  </Text>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Client
                  </Text>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Email
                  </Text>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Amount
                  </Text>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Created
                  </Text>
                  <Text style={{ fontSize: '12px', fontWeight: '600', color: '#6b7280', textTransform: 'uppercase' }}>
                    Status
                  </Text>
                </div>

                {/* Table Rows */}
                {contracts.slice(0, 5).map((contract, index) => {
                  const getStatusColor = (status: string) => {
                    switch (status) {
                      case 'SIGNED':
                      case 'COMPLETED':
                        return { bg: '#dcfce7', color: '#166534' };
                      case 'SENT':
                        return { bg: '#fef3c7', color: '#92400e' };
                      case 'DRAFT':
                        return { bg: '#f3f4f6', color: '#374151' };
                      default:
                        return { bg: '#fee2e2', color: '#dc2626' };
                    }
                  };

                  const statusStyle = getStatusColor(contract.status);

                  return (
                    <div
                      key={contract.id}
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 120px 150px 120px 120px 80px',
                        gap: '16px',
                        padding: '16px 24px',
                        borderBottom: index < contracts.slice(0, 5).length - 1 ? '1px solid rgba(0, 0, 0, 0.121569)' : 'none',
                        alignItems: 'center'
                      }}
                    >
                      <Text style={{ fontSize: '14px', fontWeight: '500', color: '#111827' }}>
                        #{contract.id.split('_')[1]?.substring(0, 8)}
                      </Text>
                      <Text style={{ fontSize: '14px', color: '#111827' }}>
                        {contract.clientInfo.name}
                      </Text>
                      <Text style={{ fontSize: '14px', color: '#6b7280' }}>
                        {contract.clientInfo.email}
                      </Text>
                      <Text style={{ fontSize: '14px', fontWeight: '500', color: '#111827' }}>
                        ${contract.contractFields?.amount || '0.00'}
                      </Text>
                      <Text style={{ fontSize: '14px', color: '#6b7280' }}>
                        {formatDate(contract.createdAt)}
                      </Text>
                      <div style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        padding: '4px 8px',
                        backgroundColor: statusStyle.bg,
                        color: statusStyle.color,
                        borderRadius: '6px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}>
                        {contract.status}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="">
        {renderContent()}
      </div>
    </div>
  );
}