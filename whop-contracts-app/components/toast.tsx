'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, X, AlertCircle, Info } from 'lucide-react';
import { Text, IconButton } from 'frosted-ui';

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'info';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  duration?: number;
}

interface ToastProps {
  toast: Toast;
  onRemove: (id: string) => void;
}

function ToastComponent({ toast, onRemove }: ToastProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onRemove(toast.id);
    }, toast.duration || 5000);

    return () => clearTimeout(timer);
  }, [toast.id, toast.duration, onRemove]);

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-white border-green-200 text-green-800 dark:bg-gray-900 dark:border-green-800 dark:text-green-200';
      case 'error':
        return 'bg-white border-red-200 text-red-800 dark:bg-gray-900 dark:border-red-800 dark:text-red-200';
      default:
        return 'bg-white border-blue-200 text-blue-800 dark:bg-gray-900 dark:border-blue-800 dark:text-blue-200';
    }
  };

  return (
    <div className={`w-full max-w-sm border rounded-lg shadow-sm ${getToastStyles()} transition-all duration-300 ease-in-out`}>
      <div className="p-3">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <Text size="2" weight="medium">
              {toast.title}
            </Text>
            {toast.message && (
              <Text size="1" className="mt-2 opacity-90">
                {toast.message}
              </Text>
            )}
            {toast.action && (
              <button
                onClick={toast.action.onClick}
                className="mt-2 text-xs font-medium underline hover:no-underline"
              >
                {toast.action.label}
              </button>
            )}
          </div>
          <IconButton
            variant="ghost"
            size="1"
            onClick={() => onRemove(toast.id)}
            className="flex-shrink-0 opacity-60 hover:opacity-100"
          >
            <X className="h-3 w-3" />
          </IconButton>
        </div>
      </div>
    </div>
  );
}

export function ToastContainer() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setToasts(prev => [...prev, { ...toast, id }]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  // Expose addToast globally
  useEffect(() => {
    (window as any).addToast = addToast;
    return () => {
      delete (window as any).addToast;
    };
  }, []);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <ToastComponent
          key={toast.id}
          toast={toast}
          onRemove={removeToast}
        />
      ))}
    </div>
  );
}

// Utility function to show toast
export function showToast(toast: Omit<Toast, 'id'>) {
  if (typeof window !== 'undefined' && (window as any).addToast) {
    (window as any).addToast(toast);
  }
}

// Utility function to ensure we have a full URL
export function ensureFullUrl(url: string): string {
  // If it's already a full URL, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // If it's a relative path, make it absolute using current origin
  if (typeof window !== 'undefined') {
    return `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;
  }
  
  // Fallback for server-side
  return url;
}

// Utility function to copy to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // Ensure we have a full URL before copying
    const fullText = text.includes('/contract/') ? ensureFullUrl(text) : text;
    
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(fullText);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = fullText;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}
