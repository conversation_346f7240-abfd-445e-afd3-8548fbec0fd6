'use client';

import { useState } from 'react';
import { useNotifications } from '@/hooks/use-notifications';
import { Bell, Check, CheckCheck, X, Eye, FileText, DollarSign, Send, CreditCard } from 'lucide-react';
import { <PERSON><PERSON>, Text, Badge, IconButton } from 'frosted-ui';

export function NotificationsPanel() {
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'contract_viewed':
        return <Eye className="h-5 w-5 text-blue-600" />;
      case 'contract_signed':
        return <Check className="h-5 w-5 text-green-600" />;
      case 'contract_sent':
        return <Send className="h-5 w-5 text-purple-600" />;
      case 'invoice_created':
        return <FileText className="h-5 w-5 text-orange-600" />;
      case 'invoice_paid':
        return <CreditCard className="h-5 w-5 text-green-600" />;
      default:
        return <Bell className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <div className="relative">
        <IconButton
          variant="ghost"
          onClick={() => setIsOpen(!isOpen)}
        >
          <Bell className="h-6 w-6" />
        </IconButton>
        {unreadCount > 0 && (
          <Badge 
            color="red" 
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </div>

      {/* Notifications Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 z-50 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <Text size="4" weight="bold">Notifications</Text>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <IconButton
                    variant="ghost"
                    size="1"
                    onClick={markAllAsRead}
                  >
                    <CheckCheck className="h-4 w-4" />
                  </IconButton>
                )}
                <IconButton
                  variant="ghost"
                  size="1"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </IconButton>
              </div>
            </div>
            {unreadCount > 0 && (
              <Text size="2" color="gray" className="mt-1">
                {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </Text>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <Text color="gray">No notifications yet</Text>
                <Text size="2" color="gray" className="mt-1">
                  You'll see updates about your contracts here
                </Text>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                      !notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                    onClick={() => {
                      if (!notification.read) {
                        markAsRead(notification.id);
                      }
                    }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <Text 
                            size="2" 
                            weight="medium"
                            color={!notification.read ? undefined : "gray"}
                          >
                            {notification.title}
                          </Text>
                          <Text size="1" color="gray">
                            {formatTime(notification.timestamp)}
                          </Text>
                        </div>
                        <Text 
                          size="2" 
                          className="mt-1"
                          color={!notification.read ? "gray" : "gray"}
                        >
                          {notification.message}
                        </Text>
                        {(notification.contractId || notification.invoiceId) && (
                          <div className="flex items-center space-x-2 mt-2">
                            {notification.contractId && (
                              <Badge color="gray" size="1">
                                Contract #{notification.contractId.split('_')[1]?.substring(0, 8)}
                              </Badge>
                            )}
                            {notification.invoiceId && (
                              <Badge color="green" size="1">
                                Invoice #{notification.invoiceId.split('_')[1]?.substring(0, 8)}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      {!notification.read && (
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {notifications.length > 0 && (
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <Button variant="ghost" size="2" className="w-full">
                View All Notifications
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}