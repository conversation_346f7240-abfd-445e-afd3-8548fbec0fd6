'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ContractData, Invoice } from '@/lib/types';
import { InvoiceData, createWhopInvoice, generateInvoiceHTML, validateInvoiceData } from '@/lib/invoice-utils';
import { exportToPDF } from '@/lib/export-utils';
import { notificationService } from '@/lib/notifications';
import { Calendar, DollarSign, FileText, Send, Eye } from 'lucide-react';

interface InvoiceGeneratorProps {
  contract: ContractData;
  companyId: string;
  onInvoiceCreated: (invoice: Invoice) => void;
  onClose: () => void;
}

const invoiceSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  description: z.string().min(1, 'Description is required'),
  dueDate: z.string().min(1, 'Due date is required'),
  paymentMethod: z.string().optional(),
  terms: z.string().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountAmount: z.number().min(0).optional(),
});

type InvoiceFormData = z.infer<typeof invoiceSchema>;

export function InvoiceGenerator({ contract, companyId, onInvoiceCreated, onClose }: InvoiceGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHTML, setPreviewHTML] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      amount: 0,
      description: `Services for Contract #${contract.id.split('_')[1]?.substring(0, 8)}`,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      paymentMethod: 'Credit Card',
      terms: 'Payment is due within 30 days of invoice date. Late payments may incur additional fees.',
      taxRate: 0,
      discountAmount: 0,
    }
  });

  const formData = watch();

  const generatePreview = () => {
    const invoiceData: InvoiceData = {
      amount: formData.amount,
      description: formData.description,
      dueDate: new Date(formData.dueDate),
      paymentMethod: formData.paymentMethod,
      terms: formData.terms,
      taxRate: formData.taxRate,
      discountAmount: formData.discountAmount,
    };

    const validation = validateInvoiceData(invoiceData);
    if (!validation.isValid) {
      alert('Please fix the validation errors: ' + validation.errors.join(', '));
      return;
    }

    // Create a mock invoice for preview
    const mockInvoice: Invoice = {
      id: 'preview_invoice',
      contractId: contract.id,
      amount: invoiceData.amount as any, // Type assertion for client-side preview
      dueDate: invoiceData.dueDate,
      status: 'PENDING',
      description: invoiceData.description,
      paymentMethod: invoiceData.paymentMethod || null,
      whopInvoiceId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const html = generateInvoiceHTML(mockInvoice, contract, invoiceData);
    setPreviewHTML(html);
    setShowPreview(true);
  };

  const onSubmit = async (data: InvoiceFormData) => {
    setIsGenerating(true);

    try {
      const invoiceData: InvoiceData = {
        amount: data.amount,
        description: data.description,
        dueDate: new Date(data.dueDate),
        paymentMethod: data.paymentMethod,
        terms: data.terms,
        taxRate: data.taxRate,
        discountAmount: data.discountAmount,
      };

      const invoice = await createWhopInvoice(contract, invoiceData, companyId);
      
      // Add notification for invoice creation
      notificationService.notifyInvoiceCreated(invoice.id, contract.id, Number(invoice.amount));
      
      onInvoiceCreated(invoice);
    } catch (error) {
      console.error('Error creating invoice:', error);
      alert('Failed to create invoice. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadPreviewPDF = async () => {
    if (!previewHTML) return;
    
    try {
      await exportToPDF(previewHTML, {
        format: 'pdf',
        filename: `invoice-${contract.id.split('_')[1]?.substring(0, 8)}.pdf`
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export PDF');
    }
  };

  const calculateTotal = () => {
    const subtotal = formData.amount || 0;
    const tax = formData.taxRate ? (subtotal * formData.taxRate) / 100 : 0;
    const discount = formData.discountAmount || 0;
    return subtotal + tax - discount;
  };

  if (showPreview) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semi-bold text-gray-900">Invoice Preview</h2>
              <div className="flex space-x-3">
                <button
                  onClick={downloadPreviewPDF}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download PDF
                </button>
                <button
                  onClick={() => setShowPreview(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back to Form
                </button>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <div dangerouslySetInnerHTML={{ __html: previewHTML }} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semi-bold text-gray-900">Generate Invoice</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          <p className="text-gray-600 mt-2">
            Create an invoice for Contract #{contract.id.split('_')[1]?.substring(0, 8)}
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="space-y-6">
            {/* Contract Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semi-bold text-gray-900 mb-2">Contract Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Client:</span>
                  <p className="font-medium">{contract.clientInfo.name}</p>
                </div>
                <div>
                  <span className="text-gray-500">Email:</span>
                  <p className="font-medium">{contract.clientInfo.email}</p>
                </div>
              </div>
            </div>

            {/* Invoice Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Invoice Amount *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                {...register('amount', { valueAsNumber: true })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
              {errors.amount && (
                <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe the services or products..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Due Date *
              </label>
              <input
                type="date"
                {...register('dueDate')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.dueDate && (
                <p className="mt-1 text-sm text-red-600">{errors.dueDate.message}</p>
              )}
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Method
              </label>
              <select
                {...register('paymentMethod')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Credit Card">Credit Card</option>
                <option value="Bank Transfer">Bank Transfer</option>
                <option value="PayPal">PayPal</option>
                <option value="Check">Check</option>
                <option value="Cash">Cash</option>
              </select>
            </div>

            {/* Tax and Discount */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Rate (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  {...register('taxRate', { valueAsNumber: true })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Discount Amount ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('discountAmount', { valueAsNumber: true })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Payment Terms */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Terms
              </label>
              <textarea
                {...register('terms')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter payment terms and conditions..."
              />
            </div>

            {/* Total Preview */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex justify-between items-center">
                <span className="font-semi-bold text-gray-900">Total Amount:</span>
                <span className="text-2xl font-bold text-blue-600">
                  ${calculateTotal().toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={generatePreview}
              className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview Invoice
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isGenerating}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <Send className="h-4 w-4 mr-2" />
                {isGenerating ? 'Creating...' : 'Create Invoice'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}