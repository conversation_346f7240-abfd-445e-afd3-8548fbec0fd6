'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { getTemplateById, contractTemplates } from '@/lib/contract-templates';
import { ContractData, ContractFormData, ContractTemplate } from '@/lib/types';
import { processContractTemplate, validateContractData, generateContractId } from '@/lib/contract-utils';
import { exportToPDF, exportToDocx } from '@/lib/export-utils';
import { InvoiceData, createWhopInvoice, generateInvoiceHTML, validateInvoiceData } from '@/lib/invoice-utils';
import { notificationService } from '@/lib/notifications';
import { ArrowLeft, Eye, Save, Send, Download, FileText, DollarSign, Calendar, Plus } from 'lucide-react';
import { Card, Heading, Text, Button, TextField, TextArea, Switch } from 'frosted-ui';

interface User {
  id: string;
  name?: string | null;
  username: string;
  bio?: string | null;
  phoneVerified: boolean;
  createdAt: number;
  profilePicture?: {
    sourceUrl?: string | null;
  } | null;
}

interface ContractBuilderProps {
  templateId?: string | null;
  user: User;
  userId: string;
  companyId?: string;
  onSave: (contract: ContractData) => void;
  onBack: () => void;
}

// Create dynamic schema based on template
const createValidationSchema = (template: any) => {
  const sellerInfoSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Valid email is required'),
    address: z.string().min(1, 'Address is required'),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const clientInfoSchema = z.object({
    name: z.string().min(1, 'Client name is required'),
    email: z.string().email('Valid client email is required'),
    address: z.string().min(1, 'Client address is required'),
    phone: z.string().optional(),
    businessName: z.string().optional(),
  });

  const contractFieldsSchema = z.object(
    template?.fields.reduce((acc: any, field: any) => {
      if (field.required) {
        acc[field.id] = field.type === 'email' 
          ? z.string().email(`Valid ${field.label} is required`)
          : z.string().min(1, `${field.label} is required`);
      } else {
        acc[field.id] = z.string().optional();
      }
      return acc;
    }, {}) || {}
  );

  return z.object({
    templateId: z.string(),
    sellerInfo: sellerInfoSchema,
    clientInfo: clientInfoSchema,
    contractFields: contractFieldsSchema,
  });
};

export function ContractBuilder({ templateId = null, user, userId, companyId = 'temp_company', onSave, onBack }: ContractBuilderProps) {
  const [template, setTemplate] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [showInvoiceSection, setShowInvoiceSection] = useState(false);
  const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null);
  
  // State for interactive elements
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [selectedContractType, setSelectedContractType] = useState<string>('');
  const [availableTemplates, setAvailableTemplates] = useState<ContractTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [selectedTerms, setSelectedTerms] = useState<'standard' | 'custom'>('standard');
  const [contractDuration, setContractDuration] = useState<string>('30 days');
  const [selectedPricing, setSelectedPricing] = useState<'free' | 'one-time' | 'recurring'>('one-time');
  const [contractValue, setContractValue] = useState<string>('10.00');
  const [currency, setCurrency] = useState<string>('USD');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [showContractOptions, setShowContractOptions] = useState(false);
  const [activePreviewTab, setActivePreviewTab] = useState<'contract' | 'email'>('contract');

  // Load available templates
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const response = await fetch('/api/templates');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.templates) {
            setAvailableTemplates(data.templates);
          } else {
            setAvailableTemplates(contractTemplates);
          }
        } else {
          setAvailableTemplates(contractTemplates);
        }
      } catch (error) {
        console.error('Error loading templates:', error);
        setAvailableTemplates(contractTemplates);
      }
    };
    
    loadTemplates();
  }, []);

  useEffect(() => {
    const loadTemplate = async () => {
      if (templateId) {
        try {
          const foundTemplate = await getTemplateById(templateId);
          setTemplate(foundTemplate);
          setSelectedTemplate(foundTemplate);
        } catch (error) {
          console.error('Error loading template:', error);
          // Fallback to default template
          setTemplate(null);
        }
      } else {
        // Use a custom template when no templateId is provided
        const defaultTemplate = {
          id: 'custom-template',
          name: 'Custom',
          description: 'A flexible custom contract template',
          category: 'Custom',
          fields: [
            { id: 'contract_title', label: 'Contract Title', type: 'text', placeholder: 'Enter contract title', required: true },
            { id: 'service_description', label: 'Service Description', type: 'textarea', placeholder: 'Describe the services to be provided...', required: true },
            { id: 'project_scope', label: 'Project Scope', type: 'textarea', placeholder: 'Define the project scope and deliverables...', required: true },
            { id: 'total_amount', label: 'Total Amount', type: 'number', placeholder: '0.00', required: true },
            { id: 'payment_terms', label: 'Payment Terms', type: 'text', placeholder: 'e.g., Net 30, 50% upfront', required: true },
            { id: 'start_date', label: 'Start Date', type: 'date', placeholder: 'Select start date', required: true },
            { id: 'end_date', label: 'End Date', type: 'date', placeholder: 'Select end date', required: true },
            { id: 'terms_conditions', label: 'Terms & Conditions', type: 'textarea', placeholder: 'Additional terms and conditions...', required: false },
            { id: 'governing_law', label: 'Governing Law', type: 'text', placeholder: 'e.g., State of California', required: true, defaultValue: 'State of California' }
          ],
          template: `# \{\{contractFields.contract_title\}\}

**This Agreement** is entered into on \{\{contract_date\}\} by and between:

**SERVICE PROVIDER:**
\{\{sellerInfo.name\}\}
\{\{sellerInfo.email\}\}
\{\{sellerInfo.address\}\}
\{\{sellerInfo.phone\}\}

**CLIENT:**
\{\{clientInfo.name\}\}
\{\{clientInfo.email\}\}
\{\{clientInfo.address\}\}
\{\{clientInfo.phone\}\}

## 1. SCOPE OF WORK

The Service Provider agrees to provide the following services:

\{\{contractFields.service_description\}\}

## 2. PROJECT SCOPE

**Deliverables:**
\{\{contractFields.project_scope\}\}

## 3. TIMELINE

**Project Start Date:** \{\{contractFields.start_date\}\}
**Project End Date:** \{\{contractFields.end_date\}\}

## 4. COMPENSATION

**Total Project Cost:** $\{\{contractFields.total_amount\}\}
**Payment Terms:** \{\{contractFields.payment_terms\}\}

## 5. TERMS AND CONDITIONS

\{\{contractFields.terms_conditions\}\}

## 6. INTELLECTUAL PROPERTY

Upon full payment, all intellectual property rights in the work product shall transfer to the Client, except for pre-existing intellectual property of the Service Provider.

## 7. CONFIDENTIALITY

Both parties agree to maintain confidentiality of all proprietary information disclosed during the course of this agreement.

## 8. TERMINATION

Either party may terminate this agreement with 30 days written notice.

## 9. GOVERNING LAW

This agreement shall be governed by the laws of \{\{contractFields.governing_law\}\}.

## 10. SIGNATURES

**SERVICE PROVIDER:**
_________________________
\{\{sellerInfo.name\}\}
Date: _______________

**CLIENT:**
_________________________
\{\{clientInfo.name\}\}
Date: _______________`
        };
        setTemplate(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    };

    loadTemplate();
  }, [templateId]);

  const validationSchema = template ? createValidationSchema(template) : z.object({
    templateId: z.string(),
    sellerInfo: z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Valid email is required'),
      address: z.string().min(1, 'Address is required'),
      phone: z.string().optional(),
      businessName: z.string().optional(),
    }),
    clientInfo: z.object({
      name: z.string().min(1, 'Client name is required'),
      email: z.string().email('Valid client email is required'),
      address: z.string().min(1, 'Client address is required'),
      phone: z.string().optional(),
      businessName: z.string().optional(),
    }),
    contractFields: z.record(z.string(), z.any()),
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors }
  } = useForm<ContractFormData>({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      templateId: templateId || '',
      sellerInfo: {
        name: user.name || user.username || 'John Smith',
        email: '<EMAIL>',
        address: '123 Business Ave, Suite 100, New York, NY 10001',
        phone: '+****************',
        businessName: 'Smith Consulting LLC',
      },
      clientInfo: {
        name: 'Jane Doe',
        email: '<EMAIL>',
        address: '456 Client Street, Los Angeles, CA 90210',
        phone: '+****************',
        businessName: 'Client Company Inc.',
      },
      contractFields: template?.fields.reduce((acc: any, field: any) => {
        // Fill with specific mock data based on field ID and type
        if (field.id === 'contract_date') {
          acc[field.id] = '2024-02-01';
        } else if (field.id === 'services_description') {
          acc[field.id] = 'Professional consulting services including strategy development, implementation planning, and ongoing support. Services include market analysis, business process optimization, and technology integration consulting.';
        } else if (field.id === 'start_date') {
          acc[field.id] = '2024-02-15';
        } else if (field.id === 'end_date') {
          acc[field.id] = '2024-08-15';
        } else if (field.id === 'deliverables') {
          acc[field.id] = '1. Comprehensive market analysis report\n2. Business process optimization plan\n3. Technology integration roadmap\n4. Implementation timeline and milestones\n5. Final project summary and recommendations';
        } else if (field.id === 'total_amount') {
          acc[field.id] = '15000';
        } else if (field.id === 'payment_schedule') {
          acc[field.id] = '50% upfront ($7,500), 25% at project midpoint ($3,750), 25% upon completion ($3,750)';
        } else if (field.id === 'payment_method') {
          acc[field.id] = 'Bank transfer or credit card payment';
        } else if (field.id === 'termination_notice') {
          acc[field.id] = '30';
        } else if (field.id === 'governing_law') {
          acc[field.id] = 'State of California';
        } else if (field.id === 'purpose') {
          acc[field.id] = 'Evaluation of potential business partnership and joint venture opportunities in the technology sector.';
        } else if (field.id === 'confidential_info_types') {
          acc[field.id] = 'Financial data, customer lists, proprietary technology, business strategies, marketing plans, and any other information marked as confidential.';
        } else if (field.id === 'term_years') {
          acc[field.id] = '2';
        } else if (field.id === 'license_type') {
          acc[field.id] = 'non-exclusive';
        } else if (field.id === 'licensed_property') {
          acc[field.id] = 'Software application "BusinessOptimizer Pro" including source code, documentation, and related intellectual property.';
        } else if (field.id === 'permitted_uses') {
          acc[field.id] = 'Internal business operations, customer service, and integration with existing systems. Commercial use is permitted within agreed scope.';
        } else if (field.id === 'restrictions') {
          acc[field.id] = 'Cannot reverse engineer, redistribute, or create derivative works without written permission. Cannot sublicense to third parties.';
        } else if (field.id === 'license_term') {
          acc[field.id] = '2 years with automatic renewal';
        } else if (field.id === 'termination_conditions') {
          acc[field.id] = 'Either party may terminate with 30 days written notice. Immediate termination for breach of confidentiality or payment default.';
        } else if (field.id === 'license_fee') {
          acc[field.id] = '25000';
        } else if (field.id === 'royalty_rate') {
          acc[field.id] = '5';
        } else if (field.type === 'number') {
          acc[field.id] = field.defaultValue || '1000';
        } else if (field.type === 'date') {
          acc[field.id] = field.defaultValue || '2024-02-01';
        } else if (field.type === 'textarea') {
          acc[field.id] = field.defaultValue || 'Sample textarea content for testing purposes. This field contains detailed information relevant to the contract.';
        } else {
          acc[field.id] = field.defaultValue || 'Sample text data for testing';
        }
        return acc;
      }, {}) || {},
    }
  });

  const formData = watch();

  // Set mock data after component mounts and template is loaded
  useEffect(() => {
    if (template) {
      // Set seller info
      setValue('sellerInfo.name', user.name || user.username || 'John Smith');
      setValue('sellerInfo.email', '<EMAIL>');
      setValue('sellerInfo.address', '123 Business Ave, Suite 100, New York, NY 10001');
      setValue('sellerInfo.phone', '+****************');
      setValue('sellerInfo.businessName', 'Smith Consulting LLC');

      // Set client info
      setValue('clientInfo.name', 'Jane Doe');
      setValue('clientInfo.email', '<EMAIL>');
      setValue('clientInfo.address', '456 Client Street, Los Angeles, CA 90210');
      setValue('clientInfo.phone', '+****************');
      setValue('clientInfo.businessName', 'Client Company Inc.');

      // Set contract fields with specific mock data
      template.fields.forEach((field: any) => {
        if (field.id === 'contract_date') {
          setValue(`contractFields.${field.id}`, '2024-02-01');
        } else if (field.id === 'services_description') {
          setValue(`contractFields.${field.id}`, 'Professional consulting services including strategy development, implementation planning, and ongoing support. Services include market analysis, business process optimization, and technology integration consulting.');
        } else if (field.id === 'start_date') {
          setValue(`contractFields.${field.id}`, '2024-02-15');
        } else if (field.id === 'end_date') {
          setValue(`contractFields.${field.id}`, '2024-08-15');
        } else if (field.id === 'deliverables') {
          setValue(`contractFields.${field.id}`, '1. Comprehensive market analysis report\n2. Business process optimization plan\n3. Technology integration roadmap\n4. Implementation timeline and milestones\n5. Final project summary and recommendations');
        } else if (field.id === 'total_amount') {
          setValue(`contractFields.${field.id}`, '15000');
        } else if (field.id === 'payment_schedule') {
          setValue(`contractFields.${field.id}`, '50% upfront ($7,500), 25% at project midpoint ($3,750), 25% upon completion ($3,750)');
        } else if (field.id === 'payment_method') {
          setValue(`contractFields.${field.id}`, 'Bank transfer or credit card payment');
        } else if (field.id === 'termination_notice') {
          setValue(`contractFields.${field.id}`, '30');
        } else if (field.id === 'governing_law') {
          setValue(`contractFields.${field.id}`, 'State of California');
        } else if (field.id === 'purpose') {
          setValue(`contractFields.${field.id}`, 'Evaluation of potential business partnership and joint venture opportunities in the technology sector.');
        } else if (field.id === 'confidential_info_types') {
          setValue(`contractFields.${field.id}`, 'Financial data, customer lists, proprietary technology, business strategies, marketing plans, and any other information marked as confidential.');
        } else if (field.id === 'term_years') {
          setValue(`contractFields.${field.id}`, '2');
        } else if (field.id === 'license_type') {
          setValue(`contractFields.${field.id}`, 'non-exclusive');
        } else if (field.id === 'licensed_property') {
          setValue(`contractFields.${field.id}`, 'Software application "BusinessOptimizer Pro" including source code, documentation, and related intellectual property.');
        } else if (field.id === 'permitted_uses') {
          setValue(`contractFields.${field.id}`, 'Internal business operations, customer service, and integration with existing systems. Commercial use is permitted within agreed scope.');
        } else if (field.id === 'restrictions') {
          setValue(`contractFields.${field.id}`, 'Cannot reverse engineer, redistribute, or create derivative works without written permission. Cannot sublicense to third parties.');
        } else if (field.id === 'license_term') {
          setValue(`contractFields.${field.id}`, '2 years with automatic renewal');
        } else if (field.id === 'termination_conditions') {
          setValue(`contractFields.${field.id}`, 'Either party may terminate with 30 days written notice. Immediate termination for breach of confidentiality or payment default.');
        } else if (field.id === 'license_fee') {
          setValue(`contractFields.${field.id}`, '25000');
        } else if (field.id === 'royalty_rate') {
          setValue(`contractFields.${field.id}`, '5');
        } else if (field.type === 'number') {
          setValue(`contractFields.${field.id}`, field.defaultValue || '1000');
        } else if (field.type === 'date') {
          setValue(`contractFields.${field.id}`, field.defaultValue || '2024-02-01');
        } else if (field.type === 'textarea') {
          setValue(`contractFields.${field.id}`, field.defaultValue || 'Sample textarea content for testing purposes. This field contains detailed information relevant to the contract.');
        } else {
          setValue(`contractFields.${field.id}`, field.defaultValue || 'Sample text data for testing');
        }
      });
    }
  }, [template, setValue, user]);

  const generatePreview = () => {
    if (!template) return;

    const content = processContractTemplate(
      template,
      formData.sellerInfo,
      formData.clientInfo,
      formData.contractFields
    );
    setPreviewContent(content);
    setShowPreview(true);
  };

  const handleTemplateChange = (template: ContractTemplate) => {
    setSelectedTemplate(template);
    setTemplate(template);
    setSelectedContractType(template.name);
    setValue('contractFields.contractType', template.name);
    setShowTemplateDropdown(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.template-dropdown-container')) {
        setShowTemplateDropdown(false);
      }
    };

    if (showTemplateDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTemplateDropdown]);

  const onSubmit = async (data: ContractFormData) => {
    if (!template) return;

    const validation = validateContractData(
      template,
      data.sellerInfo,
      data.clientInfo,
      data.contractFields
    );

    if (!validation.isValid) {
      alert('Please fix the validation errors: ' + validation.errors.join(', '));
      return;
    }

    const contractId = generateContractId();
    
    try {
      // Save contract to database
      const response = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: contractId,
          templateId: template.id,
          sellerInfo: data.sellerInfo,
          clientInfo: data.clientInfo,
          contractFields: data.contractFields,
          authorId: userId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create contract');
      }

      const result = await response.json();
      const contract: ContractData = {
        id: result.contract.id,
        templateId: result.contract.templateId,
        sellerInfo: result.contract.sellerInfo,
        clientInfo: result.contract.clientInfo,
        contractFields: result.contract.contractFields,
        status: result.contract.status,
        createdAt: new Date(result.contract.createdAt),
        updatedAt: new Date(result.contract.updatedAt),
      };

      // If invoice data exists, create invoice after contract
      if (invoiceData) {
        try {
          const invoice = await createWhopInvoice(contract, invoiceData, companyId);
          notificationService.notifyInvoiceCreated(invoice.id, contract.id, Number(invoice.amount));
        } catch (error) {
          console.error('Error creating invoice:', error);
          alert('Contract created successfully, but failed to create invoice. Please try again.');
        }
      }

      onSave(contract);
      
      // Generate and copy the shareable link
      const shareableLink = `${window.location.origin}/contract/${contract.id}`;
      navigator.clipboard.writeText(shareableLink);
      alert(`Contract saved and link copied to clipboard!\n\n${shareableLink}\n\nSend this link to your client to sign the contract.`);
    } catch (error) {
      console.error('Error creating contract:', error);
      alert('Failed to create contract. Please try again.');
    }
  };

  if (!template) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <p className="text-gray-500">Template not found. Please go back and select a template.</p>
          <button
            onClick={onBack}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Templates
          </button>
        </div>
      </div>
    );
  }

  if (showPreview) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="mb-6 flex items-center justify-between">
          <button
            onClick={() => setShowPreview(false)}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Editor
          </button>
          <div className="flex space-x-3">
            <button 
              onClick={async () => {
                try {
                  await exportToPDF(previewContent, {
                    format: 'pdf',
                    filename: `${template.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.pdf`
                  });
                } catch (error) {
                  alert('Failed to export PDF');
                }
              }}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </button>
            <button 
              onClick={() => {
                try {
                  exportToDocx(previewContent, {
                    format: 'docx',
                    filename: `${template.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.docx`
                  });
                } catch (error) {
                  alert('Failed to export DOCX');
                }
              }}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              <FileText className="h-4 w-4 mr-2" />
              Export Word
            </button>
            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              <Send className="h-4 w-4 mr-2" />
              Send to Client
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-8">
          <div 
            className="prose max-w-none"
            dangerouslySetInnerHTML={{ 
              __html: previewContent.replace(/\n/g, '<br/>').replace(/#{1,6}\s(.+)/g, '<h3 class="text-lg font-semi-bold mt-4 mb-2">$1</h3>') 
            }}
          />
        </div>
      </div>
    );
  }

  const steps = [
    { id: 1, name: 'Seller Information', description: 'Your business details' },
    { id: 2, name: 'Client Information', description: 'Client contact details' },
    { id: 3, name: 'Contract Terms', description: 'Customize contract terms' },
  ];

  return (
    <div className="create-contract-container">
      <div className="create-contract-background"></div>
      <div className="create-contract-content">
        {/* Header */}
        <div className="create-contract-header">
          <div className="create-contract-breadcrumb">
            <div className="breadcrumb-section">
              <div className="breadcrumb-back">
                <button onClick={onBack} className="close-icon">
                </button>
              </div>
              <div className="breadcrumb-path">
                <span className="breadcrumb-text">Contracts</span>
                <div className="breadcrumb-chevron">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fillRule="evenodd" clipRule="evenodd" d="M4.14645 10.3536C4.34171 10.5488 4.65829 10.5488 4.85355 10.3536L8.5 6.70711C8.89053 6.31659 8.89053 5.68342 8.5 5.2929L4.85355 1.64644C4.65829 1.45118 4.34171 1.45118 4.14645 1.64644C3.95118 1.8417 3.95118 2.15829 4.14645 2.35355L7.79289 6.00001L4.14645 9.64645C3.95118 9.84171 3.95118 10.1583 4.14645 10.3536Z" fill="#838383"/>
</svg>

                </div>
                <span className="breadcrumb-text active">Create Contract</span>
              </div>
            </div>
          </div>
          <div className="header-actions">
            
            <button 
              type="submit" 
              form="contract-form"
              className="primary-button"
              onClick={handleSubmit(onSubmit)}
            >
              <div className="content-container">
                <span className="button-text">Send contract</span>
              </div>
              <div className="sparkles-left"></div>
              <div className="sparkles-right"></div>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <div className="form-panel">
            <form id="contract-form" onSubmit={handleSubmit(onSubmit)}>
              {/* Customer Section */}
              <div className="customer-section">
                <div className="text-input">
                  <div className="label">Client</div>
                  <div className="root">
                    <div className="input-container">
                      <input 
                        type="text" 
                        placeholder="Find or add a client"
                        className="placeholder"
                        style={{ border: 'none', outline: 'none', background: 'transparent', width: '100%' }}
                        value={selectedClient}
                        onChange={(e) => {
                          setSelectedClient(e.target.value);
                          setValue('clientInfo.name', e.target.value);
                        }}
                      />
                      <div className="chevron-down">
                       
                      </div>
                    </div>
                  </div>
                </div>
                <div className="frequently-used-section">
                  <div className="frequently-used-label">Frequently used</div>
                  <div className="frequently-used-buttons">
                    <div className="frequently-used-row">
                      <button 
                        type="button"
                        className="frequent-button frequent-button-125"
                        onClick={() => setSelectedClient('<EMAIL>')}
                      >
                        <div className="content-container">
                          <div className="plus">
                            
                          </div>
                          <span className="button-text">+ <EMAIL></span>
                        </div>
                      </button>
                      <button 
                        type="button"
                        className="frequent-button frequent-button-128"
                        onClick={() => setSelectedClient('<EMAIL>')}
                      >
                        <div className="content-container">
                          <div className="plus">
                            
                          </div>
                          <span className="button-text">+ <EMAIL></span>
                        </div>
                      </button>
                      <button 
                        type="button"
                        className="frequent-button frequent-button-117"
                        onClick={() => setSelectedClient('<EMAIL>')}
                      >
                        <div className="content-container">
                          <div className="plus">
                            
                          </div>
                          <span className="button-text">+ <EMAIL></span>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contract Type Section */}
              <div className="product-section">
                <div className="text-input">
                  <div className="label">Contract Type</div>
                  <div className="root">
                    <div className="input-container template-dropdown-container" style={{ position: 'relative' }}>
                      <input 
                        type="text" 
                        placeholder="Select a contract template"
                        className="placeholder"
                        style={{ border: 'none', outline: 'none', background: 'transparent', width: '100%' }}
                        value={selectedTemplate?.name || selectedContractType}
                        onClick={() => setShowTemplateDropdown(!showTemplateDropdown)}
                        readOnly
                      />
                      <div className="chevron-down" onClick={() => setShowTemplateDropdown(!showTemplateDropdown)}>
                        ▼
                      </div>
                      
                      {/* Template Dropdown */}
                      {showTemplateDropdown && (
                        <div style={{
                          position: 'absolute',
                          top: '100%',
                          left: 0,
                          right: 0,
                          backgroundColor: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                          zIndex: 1000,
                          maxHeight: '200px',
                          overflowY: 'auto'
                        }}>
                          {availableTemplates.map((template) => (
                            <div
                              key={template.id}
                              onClick={() => handleTemplateChange(template)}
                              style={{
                                padding: '12px 16px',
                                cursor: 'pointer',
                                borderBottom: '1px solid #f3f4f6',
                                backgroundColor: selectedTemplate?.id === template.id ? '#f3f4f6' : 'white'
                              }}
                              onMouseEnter={(e) => {
                                if (selectedTemplate?.id !== template.id) {
                                  e.currentTarget.style.backgroundColor = '#f9fafb';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (selectedTemplate?.id !== template.id) {
                                  e.currentTarget.style.backgroundColor = 'white';
                                }
                              }}
                            >
                              <div style={{ fontWeight: '500', fontSize: '14px', color: '#111827' }}>
                                {template.name}
                              </div>
                              <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '2px' }}>
                                {template.description}
                              </div>
                              <div style={{ fontSize: '11px', color: '#9ca3af', marginTop: '2px' }}>
                                {template.category}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Contract Terms Section */}
              <div className="payment-collection-section">
                <div 
                  className="payment-collection-header"
                  onClick={() => setShowContractOptions(!showContractOptions)}
                >
                  <div className="payment-collection-title">Contract terms</div>
                  <div className="payment-collection-chevron">
                   
                  </div>
                </div>
                <div className="payment-collection-content">
                  <div className="payment-collection-content-inner">
                    <div 
                      className="radio-item"
                      onClick={() => setSelectedTerms('standard')}
                    >
                      <div className="radio-container">
                        <div className={`radio ${selectedTerms === 'standard' ? '' : 'unselected'}`}>
                          <div className="indicator"></div>
                        </div>
                      </div>
                      <span className="radio-text">Standard terms</span>
                    </div>
                    <div className="due-date-input">
                      <div className="label">
                        <div className="label-text">Contract duration</div>
                      </div>
                      <div className="root">
                        <div className="input-container">
                          <select 
                            value={contractDuration}
                            onChange={(e) => setContractDuration(e.target.value)}
                            style={{ border: 'none', outline: 'none', background: 'transparent', width: '100%', color: 'inherit' }}
                          >
                            <option value="7 days">7 days</option>
                            <option value="14 days">14 days</option>
                            <option value="30 days">30 days</option>
                            <option value="60 days">60 days</option>
                            <option value="90 days">90 days</option>
                            <option value="6 months">6 months</option>
                            <option value="1 year">1 year</option>
                          </select>
                          <div className="chevron-down">
                          
                          </div>
                        </div>
                      </div>
                    </div>
                    <div 
                      className="radio-item"
                      onClick={() => setSelectedTerms('custom')}
                    >
                      <div className="radio-container">
                        <div className={`radio ${selectedTerms === 'custom' ? '' : 'unselected'}`}>
                          <div className="indicator"></div>
                        </div>
                      </div>
                      <span className="radio-text autocharge">Custom terms</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Invoice Section */}
              <div className={`price-type-section ${showInvoiceSection ? 'expanded' : ''}`}>
                <div className="price-type-header">
                  <div className="price-type-title">
                    <span className="price-type-text">Invoice - ${contractValue} / {selectedPricing === 'one-time' ? 'One-time' : selectedPricing === 'recurring' ? 'Recurring' : 'Free'}</span>
                  </div>
                  <div className="price-type-chevron" style={{ marginLeft: '-10px', marginRight: '8px' }}>
                    <Switch
                      checked={showInvoiceSection}
                      onCheckedChange={setShowInvoiceSection}
                    />
                  </div>
                </div>
                <div className={`price-input-section ${showInvoiceSection ? 'expanded' : ''}`}>
                      <div className="segmented-control">
                        <button 
                          type="button"
                          className={`segmented-button ${selectedPricing === 'free' ? 'active' : ''}`}
                          onClick={() => setSelectedPricing('free')}
                        >
                          <div className="content-container">
                            <span className="button-text">Free</span>
                          </div>
                        </button>
                        <button 
                          type="button"
                          className={`segmented-button middle ${selectedPricing === 'one-time' ? 'active' : ''}`}
                          onClick={() => setSelectedPricing('one-time')}
                        >
                          <div className="content-container">
                            <span className="button-text">One-time</span>
                          </div>
                        </button>
                        <button 
                          type="button"
                          className={`segmented-button last ${selectedPricing === 'recurring' ? 'active' : ''}`}
                          onClick={() => setSelectedPricing('recurring')}
                        >
                          <div className="content-container">
                            <span className="button-text">Recurring</span>
                          </div>
                        </button>
                      </div>
                      <div className="price-input-row">
                        <div className="price-input">
                          <div className="label">
                            <div className="label-text">Invoice Amount</div>
                          </div>
                          <div className="root">
                            <div className="input-container">
                              <input 
                                type="text" 
                                placeholder="$ 10.00"
                                className="placeholder"
                                style={{ border: 'none', outline: 'none', background: 'transparent', width: '100%' }}
                                value={contractValue}
                                onChange={(e) => {
                                  setContractValue(e.target.value);
                                  setValue('contractFields.contractValue', e.target.value);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="currency-selector">
                          <div className="root">
                            <div className="input-container">
                              <span className="placeholder">{currency}</span>
                              <div className="chevron-down">
                                
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="quick-add-buttons">
                        <div className="quick-add-row">
                          <button 
                            type="button"
                            className="quick-add-button quick-add-button-68"
                            onClick={() => setContractValue('9.99')}
                          >
                            <div className="content-container">
                              <div className="plus">
                                
                              </div>
                              <span className="button-text">$9.99</span>
                            </div>
                          </button>
                          <button 
                            type="button"
                            className="quick-add-button quick-add-button-76"
                            onClick={() => setContractValue('49.99')}
                          >
                            <div className="content-container">
                              <div className="plus">
                                
                              </div>
                              <span className="button-text">$49.99</span>
                            </div>
                          </button>
                          <button 
                            type="button"
                            className="quick-add-button quick-add-button-81"
                            onClick={() => setContractValue('149.99')}
                          >
                            <div className="content-container">
                              <div className="plus">
                                
                              </div>
                              <span className="button-text">$149.99</span>
                            </div>
                          </button>
                          <button 
                            type="button"
                            className="more-button"
                            onClick={() => alert('More pricing options coming soon!')}
                          >
                            <span className="text">More</span>
                          </button>
                        </div>
                      </div>
                    </div>
              </div>

              {/* Advanced Options Section */}
              <div className="advanced-options-section">
                <div 
                  className="advanced-options-header"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                >
                  <div className="advanced-options-title">Advanced options</div>
                  <div className="advanced-options-chevron">
                    
                  </div>
                </div>
              </div>

              {/* Contract Options Section */}
              <div className="payment-options-section">
                <div 
                  className="payment-options-header"
                  onClick={() => setShowContractOptions(!showContractOptions)}
                >
                  <div className="payment-options-title">Contract options</div>
                  <div className="payment-options-chevron">
                    
                  </div>
                </div>
              </div>

              {/* Description Section */}
              <div className="description-section">
                <div className="label">Contract Description</div>
                <div className="root">
                  <div className="input-container">
                    <textarea 
                      placeholder="Add a contract description"
                      className="placeholder"
                      style={{ border: 'none', outline: 'none', background: 'transparent', width: '100%', resize: 'none' }}
                      rows={3}
                      {...register('contractFields.description')}
                    />
                  </div>
                </div>
              </div>

            </form>
          </div>

          {/* Right Panel - Preview */}
          <div className="preview-panel">
            <div className="preview-container">
              {/* Preview Tabs */}
              <div className="preview-tabs">
                <button 
                  type="button"
                  className={`preview-tab ${activePreviewTab === 'contract' ? '' : 'inactive'}`}
                  onClick={() => setActivePreviewTab('contract')}
                >
                  <div className="content-container">
                    <span className="button-text">Contract preview</span>
                  </div>
                </button>
                <button 
                  type="button"
                  className={`preview-tab ${activePreviewTab === 'email' ? '' : 'inactive'}`}
                  onClick={() => setActivePreviewTab('email')}
                >
                  <div className="content-container">
                    <span className="button-text">Email preview</span>
                  </div>
                </button>
              </div>

              {/* Contract Document Preview */}
              {activePreviewTab === 'contract' ? (
                <div className="contract-preview-document">
                  <div className="contract-preview-header">
                    <div className="contract-preview-title">
                      {template?.name || 'Service Agreement'}
                    </div>
                    <div className="contract-preview-subtitle">
                      Contract Preview
                    </div>
                  </div>
                  
                  <div className="contract-preview-content">
                    <div 
                      className="contract-document-content"
                      dangerouslySetInnerHTML={{ 
                        __html: template ? processContractTemplate(
                          template,
                          formData.sellerInfo,
                          formData.clientInfo,
                          formData.contractFields
                        ).replace(/\n/g, '<br/>').replace(/#{1,6}\s(.+)/g, '<h3 class="contract-heading">$1</h3>') : 'Select a contract template to see preview'
                      }}
                    />
                  </div>
                  
                  <div className="contract-preview-actions">
                    <button 
                      type="button"
                      className="preview-action-button"
                      onClick={() => generatePreview()}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Full Preview
                    </button>
                    <button 
                      type="button"
                      className="preview-action-button secondary"
                      onClick={async () => {
                        await onSubmit(getValues());
                      }}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Send Contract
                    </button>
                  </div>
                </div>
              ) : (
                <div className="email-preview-document">
                  <div className="email-preview-header">
                    <div className="email-preview-title">
                      Email Preview
                    </div>
                    <div className="email-preview-subtitle">
                      What your client will receive
                    </div>
                  </div>
                  
                  <div className="email-preview-content">
                    <div className="email-meta">
                      <div className="email-field">
                        <span className="email-label">To:</span>
                        <span className="email-value">{selectedClient || '<EMAIL>'}</span>
                      </div>
                      <div className="email-field">
                        <span className="email-label">From:</span>
                        <span className="email-value">{user.name || user.username} &lt;{formData.sellerInfo.email}&gt;</span>
                      </div>
                      <div className="email-field">
                        <span className="email-label">Subject:</span>
                        <span className="email-value">Contract for your signature - {template?.name || 'Service Agreement'}</span>
                      </div>
                    </div>
                    
                    <div className="email-body">
                      <p>Hi {formData.clientInfo.name || 'Client'},</p>
                      <p>I've prepared a contract for our upcoming project. Please review the attached document and let me know if you have any questions.</p>
                      <p><strong>Contract Details:</strong></p>
                      <ul>
                        <li>Service: {template?.name || 'Service Agreement'}</li>
                        <li>Value: ${contractValue}</li>
                        <li>Duration: {contractDuration}</li>
                      </ul>
                      <p>Click the link below to review and sign the contract:</p>
                      <div className="email-cta">
                        <button className="email-cta-button">Review & Sign Contract</button>
                      </div>
                      <p>Best regards,<br/>{user.name || user.username}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="preview-footer">
              <div className="preview-footer-text">Contract preview</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}