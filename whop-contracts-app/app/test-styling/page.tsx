export default function TestStyling() {
  return (
    <div className="create-contract-container">
      <div className="create-contract-background"></div>
      <div className="create-contract-content">
        {/* Header */}
        <div className="create-contract-header">
          <div className="create-contract-breadcrumb">
            <div className="breadcrumb-section">
              <div className="breadcrumb-back">
                <div className="icon">
                  <div className="union"></div>
                </div>
              </div>
              <div className="breadcrumb-path">
                <span className="breadcrumb-text">Invoicing</span>
                <div className="breadcrumb-chevron">
                  <div style={{ background: '#838383', width: '4px', height: '8px', transform: 'rotate(45deg)' }}></div>
                </div>
                <span className="breadcrumb-text active">Create Invoice</span>
              </div>
            </div>
          </div>
          <div className="header-actions">
            <div className="notification-button">
              <div className="content-container">
                <div className="icon-nav">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9Z" fill="#838383"/>
                  </svg>
                </div>
                <div className="notification-badge"></div>
              </div>
            </div>
            <div className="primary-action-button">
              <div className="content-container">
                <span className="button-text">Button</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <div className="form-panel">
            <div className="form-section">
              <h2 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '24px', color: '#202020' }}>
                Create Contract
              </h2>
              <p style={{ fontSize: '16px', color: '#8D8D8D', marginBottom: '32px' }}>
                This is the new styled create contract page as specified in SPARK-87. The layout includes:
              </p>
              <ul style={{ fontSize: '14px', color: '#202020', lineHeight: '1.6', marginLeft: '20px' }}>
                <li>Fixed container dimensions (1424px × 1007px)</li>
                <li>Header with breadcrumb navigation</li>
                <li>Notification button with badge</li>
                <li>Primary action button</li>
                <li>Form panel for contract creation</li>
                <li>Responsive design for smaller screens</li>
              </ul>
              
              <div style={{ marginTop: '32px', padding: '16px', background: '#F8F9FA', borderRadius: '8px', border: '1px solid #E8E8E8' }}>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px', color: '#202020' }}>
                  Styling Applied
                </h3>
                <p style={{ fontSize: '14px', color: '#8D8D8D' }}>
                  The CSS from the Linear issue has been implemented with:
                </p>
                <ul style={{ fontSize: '12px', color: '#8D8D8D', marginTop: '8px', marginLeft: '16px' }}>
                  <li>Exact color specifications (#FCFCFC, #E8E8E8, #1754D8, etc.)</li>
                  <li>Precise dimensions and positioning</li>
                  <li>Inter Display font family</li>
                  <li>Proper border radius and shadows</li>
                  <li>Responsive breakpoints</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}