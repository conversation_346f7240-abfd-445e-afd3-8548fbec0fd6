'use client';

import { ContractViewer } from '../../../components/contract-viewer';
import { notificationService } from '../../../lib/notifications';
import { exportToPDF } from '../../../lib/export-utils';
import { useParams } from 'next/navigation';

export default function ContractViewPage() {
  const params = useParams();
  const contractId = params.contractId as string;

  const handleContractSigned = (signedContractId: string, signatureData: string) => {
    // Contract signature has been saved to the database via the API endpoint
    // Additional actions can be performed here:
    // 1. Send confirmation emails
    // 2. Generate and send invoice
    // 3. Update external systems
    
    console.log('Contract signed:', signedContractId);
    console.log('Signature data length:', signatureData.length);
    
    // Add notification for contract signing
    notificationService.addNotification({
      type: 'contract_signed',
      title: 'Contract Signed! 🎉',
      message: `Contract ${signedContractId} has been signed successfully`,
      contractId: signedContractId,
    });
  };

  const handleDownloadPDF = async (contractId: string) => {
    try {
      // Fetch contract data
      const response = await fetch(`/api/contracts/${contractId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch contract data');
      }
      
      const { contract } = await response.json();
      
      // Generate contract content for PDF
      const contractContent = generateContractContent(contract);
      
      // Export to PDF
      await exportToPDF(contractContent, {
        format: 'pdf',
        filename: `contract-${contractId}.pdf`,
        includeWatermark: false
      });
      
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Failed to download PDF. Please try again.');
    }
  };

  const generateContractContent = (contract: any) => {
    const formatDate = (date: Date | string) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    };

    return `
      <div style="font-family: 'Times New Roman', serif; font-size: 14px; line-height: 1.6; color: #333;">
        <h1 style="text-align: center; margin-bottom: 30px; font-size: 24px; font-weight: bold;">
          CONTRACT AGREEMENT
        </h1>
        
        <div style="margin-bottom: 30px;">
          <p><strong>Contract ID:</strong> ${contract.id}</p>
          <p><strong>Status:</strong> ${contract.status}</p>
          <p><strong>Created:</strong> ${formatDate(contract.createdAt)}</p>
          ${contract.signedAt ? `<p><strong>Signed:</strong> ${formatDate(contract.signedAt)}</p>` : ''}
        </div>

        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: bold; margin-bottom: 15px;">SELLER INFORMATION</h2>
          <div style="margin-left: 20px;">
            <p><strong>Name:</strong> ${contract.sellerInfo?.name || 'N/A'}</p>
            <p><strong>Email:</strong> ${contract.sellerInfo?.email || 'N/A'}</p>
            <p><strong>Address:</strong> ${contract.sellerInfo?.address || 'N/A'}</p>
            ${contract.sellerInfo?.phone ? `<p><strong>Phone:</strong> ${contract.sellerInfo.phone}</p>` : ''}
            ${contract.sellerInfo?.businessName ? `<p><strong>Business:</strong> ${contract.sellerInfo.businessName}</p>` : ''}
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: bold; margin-bottom: 15px;">CLIENT INFORMATION</h2>
          <div style="margin-left: 20px;">
            <p><strong>Name:</strong> ${contract.clientInfo?.name || 'N/A'}</p>
            <p><strong>Email:</strong> ${contract.clientInfo?.email || 'N/A'}</p>
            <p><strong>Address:</strong> ${contract.clientInfo?.address || 'N/A'}</p>
            ${contract.clientInfo?.phone ? `<p><strong>Phone:</strong> ${contract.clientInfo.phone}</p>` : ''}
            ${contract.clientInfo?.businessName ? `<p><strong>Business:</strong> ${contract.clientInfo.businessName}</p>` : ''}
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: bold; margin-bottom: 15px;">CONTRACT TERMS</h2>
          <div style="margin-left: 20px;">
            ${contract.contractFields ? Object.entries(contract.contractFields).map(([key, value]) => `
              <div style="margin-bottom: 15px;">
                <h3 style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">${key.replace(/([A-Z])/g, ' $1').trim()}</h3>
                <p style="margin-left: 10px;">${String(value).replace(/\n/g, '<br/>')}</p>
              </div>
            `).join('') : '<p>No contract terms specified.</p>'}
          </div>
        </div>

        ${contract.clientSignature ? `
          <div style="margin-bottom: 30px;">
            <h2 style="font-size: 18px; font-weight: bold; margin-bottom: 15px;">DIGITAL SIGNATURE</h2>
            <div style="margin-left: 20px;">
              <p><strong>Status:</strong> Contract has been digitally signed</p>
              <p><strong>Signed Date:</strong> ${contract.clientSignatureAt ? formatDate(contract.clientSignatureAt) : 'Unknown'}</p>
            </div>
          </div>
        ` : ''}

        <div style="margin-top: 50px; border-top: 2px solid #333; padding-top: 20px;">
          <p style="text-align: center; font-size: 12px; color: #666;">
            This contract was generated on ${formatDate(new Date())}
          </p>
        </div>
      </div>
    `;
  };

  return (
    <ContractViewer
      contractId={contractId}
      onContractSigned={handleContractSigned}
      onDownloadPDF={handleDownloadPDF}
    />
  );
}