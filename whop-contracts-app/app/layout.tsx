import { WhopApp } from "@whop/react/components";
import { Theme } from "frosted-ui";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { ToastContainer } from "@/components/toast";
import "./globals.css";

const inter = Inter({
	variable: "--font-inter",
	subsets: ["latin"],
	weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
	display: "swap",
});

export const metadata: Metadata = {
	title: "Whop App",
	description: "My Whop App",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	// Check if Whop is configured
	const isWhopConfigured = process.env.NEXT_PUBLIC_WHOP_APP_ID && process.env.WHOP_API_KEY;

	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<link rel="preconnect" href="https://fonts.googleapis.com" />
				<link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
				<link 
					href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" 
					rel="stylesheet" 
				/>
				<script
					dangerouslySetInnerHTML={{
						__html: `
							(function() {
								try {
									var theme = localStorage.getItem('whop-contracts-theme');
									if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
										document.documentElement.classList.add('dark');
									} else {
										document.documentElement.classList.remove('dark');
									}
								} catch (e) {}
							})();
						`,
					}}
				/>
			</head>
			<body
				className={`${inter.variable} antialiased`}
			>
				<ThemeProvider>
					<Theme>
						{isWhopConfigured ? (
							<WhopApp>{children}</WhopApp>
						) : (
							children
						)}
						<ToastContainer />
					</Theme>
				</ThemeProvider>
			</body>
		</html>
	);
}
