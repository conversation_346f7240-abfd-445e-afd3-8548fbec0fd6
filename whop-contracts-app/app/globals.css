@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://api.fontshare.com/v2/css?f[]=acid-grotesk@500&display=swap');

@layer theme, base, frosted_ui, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
@import "@whop/react/styles.css" layer(frosted_ui);
@import "frosted-ui/styles.css" layer(frosted_ui);
@import "../styles/create-contract.css" layer(components);
@import "../styles/invoice-interface.css" layer(components);

@config '../tailwind.config.ts';

/* Prevent flash of incorrect theme */
html {
	color-scheme: light dark;
}

html.dark {
	color-scheme: dark;
}

html.light {
	color-scheme: light;
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: var(--font-inter), Inter, system-ui, sans-serif;
	transition: background-color 0.2s ease, color 0.2s ease;
}

/* Ensure theme transitions are smooth */
* {
	transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Disable text wrapping globally */
* {
	white-space: nowrap;
}

/* Re-enable text wrapping for specific elements that need it */
p, div, span, textarea, input[type="text"], input[type="email"], input[type="password"] {
	white-space: normal;
}

/* Allow text wrapping for specific content areas */
.receipt-total-label, .frequent-button .button-text, .quick-add-button .button-text {
	white-space: normal !important;
}

/* Ensure proper text wrapping for content areas */
.content, .description, .text-content, .message, .notification {
	white-space: normal;
}

/* Force no text wrapping in contract builder */
.create-contract-container * {
	white-space: nowrap !important;
}

/* Allow text wrapping only for input fields and textareas in contract builder */
.create-contract-container input,
.create-contract-container textarea {
	white-space: normal !important;
}

/* Ensure all button text, labels, and UI text don't wrap */
button, .button-text, .breadcrumb-text, .label, .label-text, .radio-text, .price-type-text, .placeholder, .text, .preview-footer-text {
	white-space: nowrap !important;
}

/* Override any Tailwind text wrapping classes */
.text-center, .text-left, .text-right, .text-justify, .whitespace-normal, .whitespace-pre, .whitespace-pre-line, .whitespace-pre-wrap {
	white-space: nowrap !important;
}

/* Remove grey border from WhopApp component */
[data-whop-app] {
	border: none !important;
	outline: none !important;
	box-shadow: none !important;
}

/* Remove any borders from the main container */
body > div {
	border: none !important;
	outline: none !important;
}
