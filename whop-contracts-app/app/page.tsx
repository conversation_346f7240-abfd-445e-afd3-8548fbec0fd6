import React from 'react';

export default function Page() {
	return (
		<div className="self-stretch h-[1764px] inline-flex flex-col justify-start items-start gap-4">
			<div className="self-stretch px-4 inline-flex justify-between items-center">
				<div className="w-[691.50px] flex justify-start items-center gap-2">
					<div className="flex justify-start items-center gap-3">
						<div className="px-2 py-1.5 rounded-[999px] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-Gray-colors-Gray-Alpha-5/10 backdrop-blur-[30px] flex justify-end items-center gap-2 overflow-hidden">
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative origin-top-left -rotate-90 overflow-hidden">
								<div className="w-3 h-3 left-[2px] top-[2px] absolute outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-11" />
							</div>
							<div className="justify-start text-Gray-colors-Gray-11 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Status</div>
							<div className="w-0 h-4 outline outline-1 outline-offset-[-0.50px] outline-Gray-colors-Gray-4" />
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-3.5 h-3.5 relative overflow-hidden">
								<div className="w-2.5 h-1 left-[2.33px] top-[5.25px] absolute outline outline-[1.43px] outline-offset-[-0.71px] outline-Gray-colors-Gray-11" />
							</div>
						</div>
					</div>

					<div className="flex justify-start items-center gap-3">
						<div className="px-2 py-1.5 rounded-[999px] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-Gray-colors-Gray-Alpha-5/10 backdrop-blur-[30px] flex justify-end items-center gap-2 overflow-hidden">
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative origin-top-left -rotate-90 overflow-hidden">
								<div className="w-3 h-3 left-[2px] top-[2px] absolute outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-11" />
							</div>
							<div className="justify-start text-Gray-colors-Gray-11 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Created at</div>
							<div className="w-0 h-4 outline outline-1 outline-offset-[-0.50px] outline-Gray-colors-Gray-4" />
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-3.5 h-3.5 relative overflow-hidden">
								<div className="w-2.5 h-1 left-[2.33px] top-[5.25px] absolute outline outline-[1.43px] outline-offset-[-0.71px] outline-Gray-colors-Gray-11" />
							</div>
						</div>
					</div>
					<div className="flex justify-start items-center gap-3">
						<div className="px-2 py-1.5 rounded-[999px] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-Gray-colors-Gray-Alpha-5/10 backdrop-blur-[30px] flex justify-end items-center gap-2 overflow-hidden">
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative origin-top-left -rotate-90 overflow-hidden">
								<div className="w-3 h-3 left-[2px] top-[2px] absolute outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-11" />
							</div>
							<div className="justify-start text-Gray-colors-Gray-11 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Due date</div>
							<div className="w-0 h-4 outline outline-1 outline-offset-[-0.50px] outline-Gray-colors-Gray-4" />
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-3.5 h-3.5 relative overflow-hidden">
								<div className="w-2.5 h-1 left-[2.33px] top-[5.25px] absolute outline outline-[1.43px] outline-offset-[-0.71px] outline-Gray-colors-Gray-11" />
							</div>
						</div>
					</div>
					<div className="flex justify-start items-center gap-3">
						<div className="px-2 py-1.5 rounded-[999px] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-Gray-colors-Gray-Alpha-5/10 backdrop-blur-[30px] flex justify-end items-center gap-2 overflow-hidden">
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative origin-top-left -rotate-90 overflow-hidden">
								<div className="w-3 h-3 left-[2px] top-[2px] absolute outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-11" />
							</div>
							<div className="justify-start text-Gray-colors-Gray-11 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Total</div>
							<div className="w-0 h-4 outline outline-1 outline-offset-[-0.50px] outline-Gray-colors-Gray-4" />
							<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-3.5 h-3.5 relative overflow-hidden">
								<div className="w-2.5 h-1 left-[2.33px] top-[5.25px] absolute outline outline-[1.43px] outline-offset-[-0.71px] outline-Gray-colors-Gray-11" />
							</div>
						</div>
					</div>
				</div>
				<div className="flex justify-start items-center gap-3">
					<div data-color="accent" data-focusring="false" data-highcontrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="2 ✦" data-sparkles="false" data-state="default ✦" data-variant="solid" className="inline-flex flex-col justify-start items-start">
						<div className="self-stretch h-8 px-3 bg-Accent-colors-Accent-9 rounded-lg inline-flex justify-center items-center gap-2 overflow-hidden">
							<div className="justify-center text-Tokens-Colors-contrast-accent text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight [text-shadow:_0px_1px_2px_rgb(0_0_0_/_0.05)]">Create invoice</div>
						</div>
					</div>
					<div data-color="neutral" data-focusring="false" data-highcontrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="2 ✦" data-sparkles="false" data-state="default ✦" data-variant="surface" className="inline-flex flex-col justify-start items-start">
						<div className="self-stretch h-8 px-3 bg-Panel-solid rounded-lg shadow-[0px_1px_2px_0px_rgba(0,0,0,0.06)] outline outline-1 outline-offset-[-1px] outline-Gray-colors-Gray-Alpha-5/10 inline-flex justify-center items-center gap-2 overflow-hidden">
							<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Edit</div>
						</div>
					</div>
				</div>
			</div>
			<div className="self-stretch border-t border-Gray-colors-Gray-4 inline-flex justify-start items-center overflow-hidden">
				<div className="w-36 inline-flex flex-col justify-start items-start">
					<div className="self-stretch px-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
						<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Total</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
					<div className="self-stretch px-4 py-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 h-8 justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">US$10.00</div>
					</div>
				</div>
				<div className="w-24 inline-flex flex-col justify-start items-start">
					<div className="self-stretch px-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
						<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Status</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="success" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Success-Alpha-3/10 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Success-Alpha-11/90 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Paid</div>
						</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="success" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Success-Alpha-3/10 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Success-Alpha-11/90 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Paid</div>
						</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="error" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Danger-Alpha-3/5 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Danger-Alpha-11/80 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Overdue</div>
						</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="success" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Success-Alpha-3/10 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Success-Alpha-11/90 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Paid</div>
						</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="success" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Success-Alpha-3/10 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Success-Alpha-11/90 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Paid</div>
						</div>
					</div>
					<div className="self-stretch h-12 px-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div data-color="success" data-highconstrast="false" data-iconend="false" data-iconstart="false" data-rounded="No" data-size="1 ✦" data-variant="soft ✦" className="px-2 py-0.5 bg-Semantic-colors-Success-Alpha-3/10 rounded-md flex justify-center items-center gap-1 overflow-hidden">
							<div className="justify-center text-Semantic-colors-Success-Alpha-11/90 text-xs font-medium font-['Inter_Display'] leading-none tracking-tight">Paid</div>
						</div>
					</div>
				</div>
				<div className="inline-flex flex-col justify-start items-start">
					<div className="self-stretch pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
						<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Invoice number</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0111</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0112</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0113</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0114</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0115</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">WHO-0116</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="w-48 inline-flex flex-col justify-start items-start">
					<div className="self-stretch pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
						<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Customer name</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">One Collective GmbH</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Whop, Inc.</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Stripe, Inc.</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Acme, Inc.</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Raek, Inc.</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Coinbase, Ltd.</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="inline-flex flex-col justify-start items-start">
					<div className="self-stretch pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
						<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Customer email</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
								<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
									<div className="inline-flex justify-start items-start gap-1.5">
										<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="flex justify-start items-start gap-1.5">
								<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight"><EMAIL></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="inline-flex flex-col justify-start items-start">
				<div className="self-stretch pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
					<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Due date</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">14 Aug</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="flex-1 inline-flex flex-col justify-start items-start">
				<div className="self-stretch pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-1.5 overflow-hidden">
					<div className="justify-start text-Gray-Dark text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">Created  date</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="self-stretch h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="flex-1 pr-2.5 py-1 flex justify-start items-center gap-2">
						<div className="pr-2.5 py-1 flex justify-start items-center gap-2">
							<div className="w-36 inline-flex flex-col justify-center items-start gap-2.5">
								<div className="inline-flex justify-start items-start gap-1.5">
									<div className="justify-center text-Gray-colors-Gray-12 text-sm font-medium font-['Inter_Display'] leading-tight tracking-tight">12 Aug, 09:52 AM</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="inline-flex flex-col justify-start items-start">
				<div className="self-stretch h-10 pl-6 pr-4 py-2.5 bg-Gray-colors-Gray-2 border-b border-Gray-colors-Gray-4" />
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
				<div className="h-12 pl-6 pr-4 py-3 border-b border-Gray-colors-Gray-4 inline-flex justify-start items-center gap-3">
					<div className="p-1 bg-Default-colors-Black-Alpha-2/0 rounded flex justify-start items-center gap-2">
						<div data-filled="off" data-join="round" data-radius="1" data-stroke="2" className="w-4 h-4 relative overflow-hidden">
							<div className="w-[1.33px] h-3 left-[7.33px] top-[2px] absolute bg-Gray-colors-Gray-9 outline outline-[1.63px] outline-offset-[-0.82px] outline-Gray-colors-Gray-9" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
	);
}
