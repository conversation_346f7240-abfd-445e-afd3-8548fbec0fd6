import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, templateId, sellerInfo, clientInfo, contractFields, authorId } = body

    // Validate required fields
    if (!templateId || !sellerInfo || !clientInfo || !contractFields || !authorId) {
      return NextResponse.json(
        { error: 'Missing required fields: templateId, sellerInfo, clientInfo, contractFields, authorId' },
        { status: 400 }
      )
    }

    // Check if template exists, if not create a default one
    let actualTemplateId = templateId;
    const existingTemplate = await DatabaseService.getContractTemplateById(templateId);
    
    if (!existingTemplate) {
      // If template doesn't exist (like 'default-invoice'), create a default template
      const defaultTemplate = await DatabaseService.createContractTemplate({
        name: 'Default Invoice Template',
        description: 'A simple invoice template for basic contracts',
        category: 'Invoice',
        template: `# Invoice

**From:** \{\{sellerInfo.name\}\}
**Email:** \{\{sellerInfo.email\}\}
**Address:** \{\{sellerInfo.address\}\}

**To:** \{\{clientInfo.name\}\}
**Email:** \{\{clientInfo.email\}\}
**Address:** \{\{clientInfo.address\}\}

---

**Description:** \{\{contractFields.description\}\}
**Amount:** $\{\{contractFields.amount\}\}
**Due Date:** \{\{contractFields.dueDate\}\}

---

Thank you for your business!`,
        fields: [
          { id: 'amount', label: 'Amount', type: 'text', required: true },
          { id: 'description', label: 'Description', type: 'text', required: true },
          { id: 'dueDate', label: 'Due Date', type: 'date', required: true },
        ],
        authorId: authorId
      });
      actualTemplateId = defaultTemplate.id;
    }

    // Create contract in database
    const contract = await DatabaseService.createContract({
      id,
      templateId: actualTemplateId,
      sellerInfo,
      clientInfo,
      contractFields,
      authorId
    })

    // Create notification for contract creation
    await DatabaseService.createNotification({
      type: 'CONTRACT_VIEWED',
      title: 'Contract Created',
      message: `Contract for ${clientInfo.name || clientInfo.email} has been created`,
      contractId: contract.id,
      clientEmail: clientInfo.email
    })

    return NextResponse.json({
      success: true,
      contract: {
        id: contract.id,
        templateId: contract.templateId,
        status: contract.status,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
        clientInfo: contract.clientInfo,
        sellerInfo: contract.sellerInfo,
        contractFields: contract.contractFields
      }
    })

  } catch (error) {
    console.error('Error creating contract:', error)
    return NextResponse.json(
      { error: 'Failed to create contract' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const authorId = searchParams.get('authorId')

    if (!authorId) {
      return NextResponse.json(
        { error: 'authorId is required' },
        { status: 400 }
      )
    }

    const contracts = await DatabaseService.getContractsByAuthor(authorId)

    return NextResponse.json({
      success: true,
      contracts: contracts.map(contract => ({
        id: contract.id,
        templateId: contract.templateId,
        status: contract.status,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
        signedAt: contract.signedAt,
        clientInfo: contract.clientInfo,
        sellerInfo: contract.sellerInfo,
        contractFields: contract.contractFields,
        template: contract.template
      }))
    })

  } catch (error) {
    console.error('Error fetching contracts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contracts' },
      { status: 500 }
    )
  }
}
