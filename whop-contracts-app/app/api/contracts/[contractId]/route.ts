import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contractId: string }> }
) {
  try {
    const { contractId } = await params

    const contract = await DatabaseService.getContractById(contractId)

    if (!contract) {
      return NextResponse.json(
        { error: 'Contract not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      contract: {
        id: contract.id,
        templateId: contract.templateId,
        status: contract.status,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
        signedAt: contract.signedAt,
        clientInfo: contract.clientInfo,
        sellerInfo: contract.sellerInfo,
        contractFields: contract.contractFields,
        template: contract.template,
        author: contract.author
      }
    })

  } catch (error) {
    console.error('Error fetching contract:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contract' },
      { status: 500 }
    )
  }
}
