import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'
import { EmailNotificationService } from '@/lib/notifications'
import { generateShareableLink } from '@/lib/export-utils'
import { ClientInfo } from '@/lib/types'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ contractId: string }> }
) {
  try {
    const { contractId } = await params
    const body = await request.json()
    const { clientEmail, clientName } = body

    // Get contract from database
    const contract = await DatabaseService.getContractById(contractId)
    
    if (!contract) {
      return NextResponse.json(
        { error: 'Contract not found' },
        { status: 404 }
      )
    }

    // Get client email from contract if not provided in request
    const clientInfo = contract.clientInfo as unknown as ClientInfo;
    const finalClientEmail = clientEmail || clientInfo?.email;
    const finalClientName = clientName || clientInfo?.name || 'Client';

    // Validate required fields
    if (!finalClientEmail) {
      return NextResponse.json(
        { error: 'Client email is required. Please ensure the contract has client information.' },
        { status: 400 }
      )
    }

    // Check if contract is in DRAFT status
    if (contract.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Contract must be in DRAFT status to send' },
        { status: 400 }
      )
    }

    // Generate shareable link
    const shareableLink = generateShareableLink(contractId)
    
    // Update contract status to SENT
    const updatedContract = await DatabaseService.updateContractStatus(contractId, 'SENT')

    // Send email to client
    const emailSent = await EmailNotificationService.sendContractToClient(
      finalClientEmail,
      finalClientName,
      contractId,
      (contract.template as any)?.content || '',
      shareableLink
    )

    if (!emailSent) {
      // If email failed, revert contract status
      await DatabaseService.updateContractStatus(contractId, 'DRAFT')
      return NextResponse.json(
        { error: 'Failed to send contract email' },
        { status: 500 }
      )
    }

    // Create notification for contract sent
    await DatabaseService.createNotification({
      type: 'CONTRACT_VIEWED',
      title: 'Contract Sent',
      message: `Contract has been sent to ${finalClientEmail}`,
      contractId,
      clientEmail: finalClientEmail
    })

    return NextResponse.json({
      success: true,
      contract: {
        id: updatedContract.id,
        status: updatedContract.status,
        shareableLink,
        sentAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error sending contract:', error)
    return NextResponse.json(
      { error: 'Failed to send contract' },
      { status: 500 }
    )
  }
}
