import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ contractId: string }> }
) {
  try {
    const { contractId } = await params
    const body = await request.json()
    const { signatureData, termsAccepted } = body

    // Validate required fields
    if (!signatureData) {
      return NextResponse.json(
        { error: 'Signature data is required' },
        { status: 400 }
      )
    }

    if (!termsAccepted) {
      return NextResponse.json(
        { error: 'Terms and conditions must be accepted' },
        { status: 400 }
      )
    }

    // Get client information
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Save signature to database
    const updatedContract = await DatabaseService.saveContractSignature({
      contractId,
      signatureData,
      ipAddress,
      userAgent
    })

    // Create notification for contract signing
    await DatabaseService.createNotification({
      type: 'CONTRACT_SIGNED',
      title: 'Contract Signed! 🎉',
      message: `Contract ${contractId} has been signed successfully`,
      contractId,
      clientEmail: (updatedContract.clientInfo as any)?.email
    })

    return NextResponse.json({
      success: true,
      contract: {
        id: updatedContract.id,
        status: updatedContract.status,
        signedAt: updatedContract.signedAt,
        clientSignatureAt: updatedContract.clientSignatureAt
      }
    })

  } catch (error) {
    console.error('Error saving contract signature:', error)
    return NextResponse.json(
      { error: 'Failed to save signature' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ contractId: string }> }
) {
  try {
    const { contractId } = await params

    const signatureData = await DatabaseService.getContractSignature(contractId)

    if (!signatureData) {
      return NextResponse.json(
        { error: 'Contract not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      signature: signatureData
    })

  } catch (error) {
    console.error('Error retrieving contract signature:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve signature' },
      { status: 500 }
    )
  }
}
