import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const templates = await DatabaseService.getContractTemplates()

    return NextResponse.json({
      success: true,
      templates: templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        template: template.template,
        fields: template.fields,
        isActive: template.isActive,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt
      }))
    })

  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, category, template, fields, authorId } = body

    // Validate required fields
    if (!name || !description || !category || !template || !fields) {
      return NextResponse.json(
        { error: 'Missing required fields: name, description, category, template, fields' },
        { status: 400 }
      )
    }

    // Create template in database
    const contractTemplate = await DatabaseService.createContractTemplate({
      name,
      description,
      category,
      template,
      fields,
      authorId
    })

    return NextResponse.json({
      success: true,
      template: {
        id: contractTemplate.id,
        name: contractTemplate.name,
        description: contractTemplate.description,
        category: contractTemplate.category,
        template: contractTemplate.template,
        fields: contractTemplate.fields,
        isActive: contractTemplate.isActive,
        createdAt: contractTemplate.createdAt,
        updatedAt: contractTemplate.updatedAt
      }
    })

  } catch (error) {
    console.error('Error creating template:', error)
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    )
  }
}
