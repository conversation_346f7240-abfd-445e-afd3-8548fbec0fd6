import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const authorId = searchParams.get('authorId')

    if (!authorId) {
      return NextResponse.json(
        { error: 'Author ID is required' },
        { status: 400 }
      )
    }

    const customers = await DatabaseService.getCustomersByAuthor(authorId)

    return NextResponse.json({
      success: true,
      customers
    })

  } catch (error) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, address, phone, authorId } = body

    // Validate required fields
    if (!name || !email || !authorId) {
      return NextResponse.json(
        { error: 'Name, email, and authorId are required' },
        { status: 400 }
      )
    }

    // Create customer in database
    const customer = await DatabaseService.createCustomer({
      name,
      email,
      address,
      phone,
      authorId
    })

    return NextResponse.json({
      success: true,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        address: customer.address,
        phone: customer.phone,
        createdAt: customer.createdAt
      }
    })

  } catch (error) {
    console.error('Error creating customer:', error)
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}
