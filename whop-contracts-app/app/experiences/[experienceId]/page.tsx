import { whopSdk, isWhopReady } from "@/lib/whop-sdk";
import { headers } from "next/headers";
import { ContractsDashboard } from "@/components/contracts-dashboard";
import { DatabaseService } from "@/lib/db";

export default async function ExperiencePage({
	params,
}: {
	params: Promise<{ experienceId: string }>;
}) {
	// Check if Whop is configured
	if (!isWhopReady()) {
		return (
			<div className="flex justify-center items-center h-screen px-8">
				<h1 className="text-xl text-red-600">
					Whop configuration is missing. Please set up your environment variables.
				</h1>
			</div>
		);
	}

	// The headers contains the user token
	const headersList = await headers();

	// The experienceId is a path param
	const { experienceId } = await params;

	// The user token is in the headers
	const { userId } = await whopSdk!.verifyUserToken(headersList);

	const result = await whopSdk!.access.checkIfUserHasAccessToExperience({
		userId,
		experienceId,
	});

	const currentUser = await whopSdk!.users.getCurrentUser();
	const user = currentUser?.user;
	const experience = await whopSdk!.experiences.getExperience({ experienceId });

	// Either: 'admin' | 'customer' | 'no_access';
	// 'admin' means the user is an admin of the whop, such as an owner or moderator
	// 'customer' means the user is a common member in this whop
	// 'no_access' means the user does not have access to the whop
	const { accessLevel } = result;

	if (!result.hasAccess) {
		return (
			<div className="flex justify-center items-center h-screen px-8">
				<h1 className="text-xl text-red-600">
					Access Denied. You do not have permission to access this experience.
				</h1>
			</div>
		);
	}

	// For experiences, we need to get the company information from the experience
	// Since experiences are part of companies, we can get company data through the experience
	const company = {
		id: experience.company.id,
		title: experience.company.title || experience.name, // Use company title or experience name as fallback
	};

	// Ensure user exists in our database (similar to dashboard page)
	try {
		let dbUser = await DatabaseService.getUserByWhopId(userId);
		
		if (!dbUser) {
			// Use email if available, otherwise use a placeholder based on whopId
			const email = user?.email || `${user?.username || 'user'}_${userId}@whop.local`;
			
			dbUser = await DatabaseService.createUser({
				email,
				name: user?.name || user?.username,
				whopId: userId
			});
		}
		
		// Use database user ID for contract creation
		const dbUserId = dbUser.id;
		
		return (
			<ContractsDashboard 
				user={user}
				company={company}
				companyId={experience.company.id}
				userId={dbUserId}
			/>
		);
	} catch (error) {
		console.error('Failed to create/update user in database:', error);
		// Fallback to Whop ID
		return (
			<ContractsDashboard 
				user={user}
				company={company}
				companyId={experience.company.id}
				userId={userId}
			/>
		);
	}
}
