import { whopSdk, isWhopReady } from "@/lib/whop-sdk";
import { headers } from "next/headers";
import { ContractsDashboard } from "@/components/contracts-dashboard";
import { DatabaseService } from "@/lib/db";

export default async function DashboardPage({
	params,
}: {
	params: Promise<{ companyId: string }>;
}) {
	// Check if Whop is configured
	if (!isWhopReady()) {
		return (
			<div className="flex justify-center items-center h-screen px-8">
				<h1 className="text-xl text-red-600">
					Whop configuration is missing. Please set up your environment variables.
				</h1>
			</div>
		);
	}

	// The headers contains the user token
	const headersList = await headers();

	// The companyId is a path param
	const { companyId } = await params;

	// The user token is in the headers
	const { userId } = await whopSdk!.verifyUserToken(headersList);

	const result = await whopSdk!.access.checkIfUserHasAccessToCompany({
		userId,
		companyId,
	});

	const currentUser = await whopSdk!.users.getCurrentUser();
	const user = currentUser?.user;
	const company = await whopSdk!.companies.getCompany({ companyId });

	// Either: 'admin' | 'no_access';
	// 'admin' means the user is an admin of the company, such as an owner or moderator
	// 'no_access' means the user is not an authorized member of the company
	const { accessLevel } = result;

	if (!result.hasAccess) {
		return (
			<div className="flex justify-center items-center h-screen px-8">
				<h1 className="text-xl text-red-600">
					Access Denied. You do not have permission to view this company's contracts.
				</h1>
			</div>
		);
	}

	// Ensure user exists in our database
	try {
		// Check if user exists by whopId first
		let dbUser = await DatabaseService.getUserByWhopId(userId);
		
		// If user doesn't exist, create them
		if (!dbUser) {
			// Use email if available, otherwise use a placeholder based on whopId
			const email = user?.email || `${user?.username || 'user'}_${userId}@whop.local`;
			
			dbUser = await DatabaseService.createUser({
				email,
				name: user?.name || user?.username,
				whopId: userId
			});
			console.log('Created new user in database:', dbUser.id);
		} else {
			console.log('User already exists in database:', dbUser.id);
		}
	} catch (error) {
		console.error('Failed to create/update user in database:', error);
		// Continue anyway - this shouldn't block the dashboard from loading
	}

	// Get the database user ID for contract creation
	let dbUserId = userId; // fallback to Whop ID
	try {
		const dbUser = await DatabaseService.getUserByWhopId(userId);
		if (dbUser) {
			dbUserId = dbUser.id;
		}
	} catch (error) {
		console.error('Failed to get database user ID:', error);
	}

	return (
		<ContractsDashboard 
			user={user}
			company={company}
			companyId={companyId}
			userId={dbUserId}
		/>
	);
}
