import { User, Contract, Invoice, ContractTemplate, Notification, ContractStatus, InvoiceStatus, NotificationType } from '@prisma/client'

// Re-export Prisma types for convenience
export type { User, Contract, Invoice, ContractTemplate, Notification, ContractStatus, InvoiceStatus, NotificationType }

// Extended types with relations
export type ContractWithRelations = Contract & {
  template: ContractTemplate
  author: User
  invoices: Invoice[]
  notifications: Notification[]
}

export type InvoiceWithRelations = Invoice & {
  contract: ContractWithRelations
}

export type ContractTemplateWithAuthor = ContractTemplate & {
  author?: User | null
}

// Legacy interfaces for backward compatibility
export interface SellerInfo {
  name: string;
  email: string;
  address: string;
  phone?: string;
  businessName?: string;
  taxId?: string;
}

export interface ClientInfo {
  name: string;
  email: string;
  address: string;
  phone?: string;
  businessName?: string;
}

export interface ContractData {
  id: string;
  templateId: string;
  sellerInfo: SellerInfo;
  clientInfo: ClientInfo;
  contractFields: Record<string, any>;
  status: ContractStatus;
  createdAt: Date;
  updatedAt: Date;
  signedAt?: Date;
  clientSignature?: string;
  clientSignatureAt?: Date;
  ipAddress?: string;
  userAgent?: string;
  invoiceId?: string;
}

export interface InvoiceData {
  id: string;
  contractId: string;
  amount: number;
  dueDate: Date;
  status: InvoiceStatus;
  paymentMethod?: string;
  description: string;
  createdAt: Date;
}

export interface ContractFormData {
  templateId: string;
  sellerInfo: SellerInfo;
  clientInfo: ClientInfo;
  contractFields: Record<string, any>;
}

export interface ExportOptions {
  format: 'pdf' | 'docx';
  includeInvoice: boolean;
  watermark?: string;
}

// Database operation types
export interface CreateContractData {
  templateId: string;
  sellerInfo: SellerInfo;
  clientInfo: ClientInfo;
  contractFields: Record<string, any>;
  authorId: string;
}

export interface CreateInvoiceData {
  contractId: string;
  amount: number;
  dueDate: Date;
  description: string;
  paymentMethod?: string;
  whopInvoiceId?: string;
}

export interface CreateNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  contractId?: string;
  clientEmail?: string;
  invoiceId?: string;
}