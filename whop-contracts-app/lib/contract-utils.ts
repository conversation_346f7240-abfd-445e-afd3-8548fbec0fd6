import { ContractData, SellerInfo, ClientInfo } from './types';
import { ContractTemplate } from './contract-templates';

export function processContractTemplate(
  template: ContractTemplate,
  sellerInfo: SellerInfo,
  clientInfo: ClientInfo,
  contractFields: Record<string, any>
): string {
  let processedTemplate = template.template;

  // Replace seller info placeholders with the correct format
  processedTemplate = processedTemplate.replace(/\{\{sellerInfo\.name\}\}/g, sellerInfo.name);
  processedTemplate = processedTemplate.replace(/\{\{sellerInfo\.email\}\}/g, sellerInfo.email);
  processedTemplate = processedTemplate.replace(/\{\{sellerInfo\.address\}\}/g, sellerInfo.address);
  processedTemplate = processedTemplate.replace(/\{\{sellerInfo\.phone\}\}/g, sellerInfo.phone || '');
  processedTemplate = processedTemplate.replace(/\{\{sellerInfo\.businessName\}\}/g, sellerInfo.businessName || '');

  // Replace client info placeholders with the correct format
  processedTemplate = processedTemplate.replace(/\{\{clientInfo\.name\}\}/g, clientInfo.name);
  processedTemplate = processedTemplate.replace(/\{\{clientInfo\.email\}\}/g, clientInfo.email);
  processedTemplate = processedTemplate.replace(/\{\{clientInfo\.address\}\}/g, clientInfo.address);
  processedTemplate = processedTemplate.replace(/\{\{clientInfo\.phone\}\}/g, clientInfo.phone || '');
  processedTemplate = processedTemplate.replace(/\{\{clientInfo\.businessName\}\}/g, clientInfo.businessName || '');

  // Replace contract field placeholders with the correct format
  Object.entries(contractFields).forEach(([key, value]) => {
    const placeholder = new RegExp(`\\{\\{contractFields\\.${key}\\}\\}`, 'g');
    processedTemplate = processedTemplate.replace(placeholder, String(value || ''));
  });

  // Replace contract_date placeholder
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  processedTemplate = processedTemplate.replace(/\{\{contract_date\}\}/g, currentDate);

  return processedTemplate;
}

export function validateContractData(
  template: ContractTemplate,
  sellerInfo: SellerInfo,
  clientInfo: ClientInfo,
  contractFields: Record<string, any>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate seller info
  if (!sellerInfo.name) errors.push('Seller name is required');
  if (!sellerInfo.email) errors.push('Seller email is required');
  if (!sellerInfo.address) errors.push('Seller address is required');

  // Validate client info
  if (!clientInfo.name) errors.push('Client name is required');
  if (!clientInfo.email) errors.push('Client email is required');
  if (!clientInfo.address) errors.push('Client address is required');

  // Validate required contract fields
  template.fields.forEach(field => {
    if (field.required && !contractFields[field.id]) {
      errors.push(`${field.label} is required`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
}

export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function generateContractId(): string {
  // Use crypto.randomUUID() if available, otherwise fallback to timestamp + random
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return `contract_${crypto.randomUUID()}`;
  }
  // Fallback for environments without crypto.randomUUID
  return `contract_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${Math.random().toString(36).substr(2, 9)}`;
}

export function generateInvoiceId(): string {
  return `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}