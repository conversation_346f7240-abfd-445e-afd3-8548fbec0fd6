import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query'],
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Database utility functions
export class DatabaseService {
  // User operations
  static async createUser(data: {
    whopId?: string
    email: string
    name?: string
  }) {
    return prisma.user.create({
      data
    })
  }

  static async getUserByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email }
    })
  }

  static async getUserByWhopId(whopId: string) {
    return prisma.user.findUnique({
      where: { whopId }
    })
  }

  // Contract template operations
  static async getContractTemplates() {
    return prisma.contractTemplate.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    })
  }

  static async getContractTemplateById(id: string) {
    return prisma.contractTemplate.findUnique({
      where: { id }
    })
  }

  static async createContractTemplate(data: {
    name: string
    description: string
    category: string
    template: string
    fields: any[]
    authorId?: string
  }) {
    return prisma.contractTemplate.create({
      data: {
        ...data,
        fields: data.fields
      }
    })
  }

  // Contract operations
  static async createContract(data: {
    id?: string
    templateId: string
    sellerInfo: any
    clientInfo: any
    contractFields: any
    authorId: string
  }) {
    try {
      return await prisma.contract.create({
        data: {
          ...data,
          ...(data.id && { id: data.id })
        },
        include: {
          template: true,
          author: true
        }
      })
    } catch (error: any) {
      // Handle unique constraint violation (duplicate ID)
      if (error.code === 'P2002' && error.meta?.target?.includes('id')) {
        // Generate a new unique ID and retry once
        const newId = `contract_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${Math.random().toString(36).substr(2, 9)}`;
        return await prisma.contract.create({
          data: {
            ...data,
            id: newId
          },
          include: {
            template: true,
            author: true
          }
        })
      }
      throw error;
    }
  }

  static async getContractById(id: string) {
    return prisma.contract.findUnique({
      where: { id },
      include: {
        template: true,
        author: true,
        invoices: true,
        notifications: true
      }
    })
  }

  static async getContractsByAuthor(authorId: string) {
    return prisma.contract.findMany({
      where: { authorId },
      include: {
        template: true,
        invoices: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  static async updateContractStatus(id: string, status: 'DRAFT' | 'SENT' | 'SIGNED' | 'COMPLETED', signedAt?: Date) {
    return prisma.contract.update({
      where: { id },
      data: {
        status,
        ...(signedAt && { signedAt })
      }
    })
  }

  // Signature operations
  static async saveContractSignature(data: {
    contractId: string
    signatureData: string
    ipAddress?: string
    userAgent?: string
  }) {
    const now = new Date()
    return prisma.contract.update({
      where: { id: data.contractId },
      data: {
        status: 'SIGNED',
        clientSignature: data.signatureData,
        clientSignatureAt: now,
        signedAt: now,
        ...(data.ipAddress && { ipAddress: data.ipAddress }),
        ...(data.userAgent && { userAgent: data.userAgent })
      },
      include: {
        template: true,
        author: true
      }
    })
  }

  static async getContractSignature(contractId: string) {
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
      select: {
        clientSignature: true,
        clientSignatureAt: true,
        ipAddress: true,
        userAgent: true,
        status: true
      }
    })
    return contract
  }

  // Invoice operations
  static async createInvoice(data: {
    contractId: string
    amount: number
    dueDate: Date
    description: string
    paymentMethod?: string
    whopInvoiceId?: string
  }) {
    return prisma.invoice.create({
      data
    })
  }

  static async getInvoiceById(id: string) {
    return prisma.invoice.findUnique({
      where: { id },
      include: {
        contract: {
          include: {
            template: true,
            author: true
          }
        }
      }
    })
  }

  static async updateInvoiceStatus(id: string, status: 'PENDING' | 'PAID' | 'OVERDUE') {
    return prisma.invoice.update({
      where: { id },
      data: { status }
    })
  }

  // Notification operations
  static async createNotification(data: {
    type: 'CONTRACT_VIEWED' | 'CONTRACT_SIGNED' | 'INVOICE_PAID' | 'CONTRACT_EXPIRED' | 'PAYMENT_REMINDER'
    title: string
    message: string
    contractId?: string
    clientEmail?: string
    invoiceId?: string
  }) {
    return prisma.notification.create({
      data
    })
  }

  static async getNotificationsByContract(contractId: string) {
    return prisma.notification.findMany({
      where: { contractId },
      orderBy: { createdAt: 'desc' }
    })
  }

  static async markNotificationAsRead(id: string) {
    return prisma.notification.update({
      where: { id },
      data: { isRead: true }
    })
  }

  static async getUnreadNotifications(contractId?: string) {
    return prisma.notification.findMany({
      where: {
        isRead: false,
        ...(contractId && { contractId })
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  // Customer operations
  static async createCustomer(data: {
    name: string
    email: string
    address?: string
    phone?: string
    authorId: string
  }) {
    return prisma.customer.create({
      data
    })
  }

  static async getCustomersByAuthor(authorId: string) {
    return prisma.customer.findMany({
      where: { authorId },
      orderBy: { createdAt: 'desc' }
    })
  }

  static async getCustomerById(id: string) {
    return prisma.customer.findUnique({
      where: { id }
    })
  }

  static async updateCustomer(id: string, data: {
    name?: string
    email?: string
    address?: string
    phone?: string
  }) {
    return prisma.customer.update({
      where: { id },
      data
    })
  }

  static async deleteCustomer(id: string) {
    return prisma.customer.delete({
      where: { id }
    })
  }
}

// Export the prisma client for direct use when needed
// prisma is already exported above
