export interface Notification {
  id: string;
  type: 'contract_viewed' | 'contract_signed' | 'invoice_paid' | 'contract_expired' | 'payment_reminder';
  title: string;
  message: string;
  contractId?: string;
  invoiceId?: string;
  clientEmail?: string;
  timestamp: Date;
  read: boolean;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  contractViewed: boolean;
  contractSigned: boolean;
  invoicePaid: boolean;
  contractSent: boolean;
}

class NotificationService {
  private notifications: Notification[] = [];
  private listeners: ((notifications: Notification[]) => void)[] = [];

  // Add a new notification
  addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>): void {
    const newNotification: Notification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      read: false,
    };

    this.notifications.unshift(newNotification);
    this.notifyListeners();

    // In a real implementation, you might also:
    // 1. Store in database
    // 2. Send email notifications
    // 3. Send push notifications
    // 4. Integrate with <PERSON><PERSON>'s notification system
  }

  // Get all notifications
  getNotifications(): Notification[] {
    return [...this.notifications];
  }

  // Get unread notifications
  getUnreadNotifications(): Notification[] {
    return this.notifications.filter(n => !n.read);
  }

  // Mark notification as read
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners();
    }
  }

  // Mark all notifications as read
  markAllAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    this.notifyListeners();
  }

  // Subscribe to notification updates
  subscribe(listener: (notifications: Notification[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.notifications]));
  }

  // Contract-specific notification helpers
  notifyContractSent(contractId: string, clientEmail: string): void {
    this.addNotification({
      type: 'contract_viewed',
      title: 'Contract Sent',
      message: `Contract has been sent to ${clientEmail}`,
      contractId,
      clientEmail,
    });
  }

  notifyContractViewed(contractId: string, clientEmail: string): void {
    this.addNotification({
      type: 'contract_viewed',
      title: 'Contract Viewed',
      message: `${clientEmail} has viewed the contract`,
      contractId,
      clientEmail,
    });
  }

  notifyContractSigned(contractId: string, clientEmail: string): void {
    this.addNotification({
      type: 'contract_signed',
      title: 'Contract Signed! 🎉',
      message: `${clientEmail} has signed the contract`,
      contractId,
      clientEmail,
    });
  }

  notifyInvoiceCreated(invoiceId: string, contractId: string, amount: number): void {
    this.addNotification({
      type: 'contract_viewed',
      title: 'Invoice Created',
      message: `Invoice for $${amount.toFixed(2)} has been created`,
      contractId,
      invoiceId,
    });
  }

  notifyInvoicePaid(invoiceId: string, contractId: string, amount: number): void {
    this.addNotification({
      type: 'invoice_paid',
      title: 'Payment Received! 💰',
      message: `Payment of $${amount.toFixed(2)} has been received`,
      contractId,
      invoiceId,
    });
  }
}

// Create a singleton instance
export const notificationService = new NotificationService();

// Email notification service
export class EmailNotificationService {
  static async sendContractToClient(
    clientEmail: string,
    clientName: string,
    contractId: string,
    contractContent: string,
    shareableLink: string
  ): Promise<boolean> {
    try {
      // In a real implementation, this would use a service like SendGrid, Mailgun, or AWS SES
      console.log('Sending contract email to:', clientEmail);
      
      const emailContent = `
        Dear ${clientName},
        
        You have received a new contract for review and signature.
        
        Please review the contract and sign it using the link below:
        ${shareableLink}
        
        If you have any questions, please don't hesitate to contact us.
        
        Best regards,
        The Contracts Team
      `;

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Notify that contract was sent
      notificationService.notifyContractSent(contractId, clientEmail);
      
      return true;
    } catch (error) {
      console.error('Error sending contract email:', error);
      return false;
    }
  }

  static async sendInvoiceToClient(
    clientEmail: string,
    clientName: string,
    invoiceId: string,
    contractId: string,
    amount: number,
    invoiceHTML: string
  ): Promise<boolean> {
    try {
      console.log('Sending invoice email to:', clientEmail);
      
      const emailContent = `
        Dear ${clientName},
        
        Thank you for signing the contract. Please find your invoice attached.
        
        Invoice Amount: $${amount.toFixed(2)}
        Contract ID: ${contractId}
        Invoice ID: ${invoiceId}
        
        Please note that the contract must be signed before payment can be processed.
        
        Best regards,
        The Contracts Team
      `;

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (error) {
      console.error('Error sending invoice email:', error);
      return false;
    }
  }
}

// React hook for using notifications (to be used in components)
// Note: This would need to be moved to a separate React component file
export const notificationHookCode = `
import { useState, useEffect } from 'react';
import { notificationService, Notification } from '@/lib/notifications';

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    const unsubscribe = notificationService.subscribe(setNotifications);
    setNotifications(notificationService.getNotifications());
    return unsubscribe;
  }, []);

  return {
    notifications,
    unreadCount: notifications.filter(n => !n.read).length,
    markAsRead: notificationService.markAsRead.bind(notificationService),
    markAllAsRead: notificationService.markAllAsRead.bind(notificationService),
  };
}
`;