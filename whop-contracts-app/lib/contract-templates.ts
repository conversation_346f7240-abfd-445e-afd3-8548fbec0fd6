export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template: string;
  fields: ContractField[];
}

export interface ContractField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'date' | 'number' | 'email';
  placeholder: string;
  required: boolean;
  defaultValue?: string;
}

export const contractTemplates: ContractTemplate[] = [
  {
    id: 'custom-template',
    name: 'Custom',
    description: 'A flexible custom contract template',
    category: 'Custom',
    template: `# \{\{contractFields.contract_title\}\}

**This Agreement** is entered into on \{\{contract_date\}\} by and between:

**SERVICE PROVIDER:**
\{\{sellerInfo.name\}\}
\{\{sellerInfo.email\}\}
\{\{sellerInfo.address\}\}
\{\{sellerInfo.phone\}\}

**CLIENT:**
\{\{clientInfo.name\}\}
\{\{clientInfo.email\}\}
\{\{clientInfo.address\}\}
\{\{clientInfo.phone\}\}

## 1. SCOPE OF WORK

The Service Provider agrees to provide the following services:

\{\{contractFields.service_description\}\}

## 2. PROJECT SCOPE

**Deliverables:**
\{\{contractFields.project_scope\}\}

## 3. TIMELINE

**Project Start Date:** \{\{contractFields.start_date\}\}
**Project End Date:** \{\{contractFields.end_date\}\}

## 4. COMPENSATION

**Total Project Cost:** $\{\{contractFields.total_amount\}\}
**Payment Terms:** \{\{contractFields.payment_terms\}\}

## 5. TERMS AND CONDITIONS

\{\{contractFields.terms_conditions\}\}

## 6. INTELLECTUAL PROPERTY

Upon full payment, all intellectual property rights in the work product shall transfer to the Client, except for pre-existing intellectual property of the Service Provider.

## 7. CONFIDENTIALITY

Both parties agree to maintain confidentiality of all proprietary information disclosed during the course of this agreement.

## 8. TERMINATION

Either party may terminate this agreement with 30 days written notice.

## 9. GOVERNING LAW

This agreement shall be governed by the laws of \{\{contractFields.governing_law\}\}.

## 10. SIGNATURES

**SERVICE PROVIDER:**
_________________________
\{\{sellerInfo.name\}\}
Date: _______________

**CLIENT:**
_________________________
\{\{clientInfo.name\}\}
Date: _______________`,
    fields: [
      { id: 'contract_title', label: 'Contract Title', type: 'text', placeholder: 'Enter contract title', required: true },
      { id: 'service_description', label: 'Service Description', type: 'textarea', placeholder: 'Describe the services to be provided...', required: true },
      { id: 'project_scope', label: 'Project Scope', type: 'textarea', placeholder: 'Define the project scope and deliverables...', required: true },
      { id: 'total_amount', label: 'Total Amount', type: 'number', placeholder: '0.00', required: true },
      { id: 'payment_terms', label: 'Payment Terms', type: 'text', placeholder: 'e.g., Net 30, 50% upfront', required: true },
      { id: 'start_date', label: 'Start Date', type: 'date', placeholder: 'Select start date', required: true },
      { id: 'end_date', label: 'End Date', type: 'date', placeholder: 'Select end date', required: true },
      { id: 'terms_conditions', label: 'Terms & Conditions', type: 'textarea', placeholder: 'Additional terms and conditions...', required: false },
      { id: 'governing_law', label: 'Governing Law', type: 'text', placeholder: 'e.g., State of California', required: true, defaultValue: 'State of California' }
    ]
  },
  {
    id: 'service-agreement',
    name: 'Freelance Agreement',
    description: 'A comprehensive freelance contract for independent contractors',
    category: 'Service',
    template: `# FREELANCE CONTRACT

This Freelance Contract (this "Agreement") is made as of this \{\{contractFields.effective_date\}\} (the "Effective Date") by and between \{\{clientInfo.name\}\} located at \{\{clientInfo.address\}\} ("Client") and \{\{sellerInfo.name\}\} located at \{\{sellerInfo.address\}\} ("Independent Contractor").

Client and Independent Contractor may each be referred to in this Agreement as a "Party" and collectively as the "Parties."

## 1. Services

Independent Contractor shall provide the following services to Client (the "Services"): \{\{contractFields.services_description\}\}. In addition, Independent Contractor shall perform such other duties and tasks, or changes to the Services, as may be agreed upon by the Parties.

## 2. Compensation

In consideration for Independent Contractor's performance of the Services, Client shall pay Independent Contractor \{\{contractFields.compensation_amount\}\}.

## 3. Expenses

All costs and expenses incurred by Independent Contractor in connection with the performance of the Services shall be the sole responsibility of and paid by Independent Contractor.

## 4. Term and Termination

Independent Contractor's engagement with Client under this Agreement shall commence on \{\{contractFields.start_date\}\}. The Parties agree and acknowledge that this Agreement and Independent Contractor's engagement with Client under this Agreement shall terminate on \{\{contractFields.end_date\}\}.

At the time of termination, Independent Contractor agrees to return all Client property used in performance of the Services, including but not limited to computers, cell phones, keys, reports, and other equipment and documents. Independent Contractor shall reimburse Client for any Client property lost or damaged in an amount equal to the market price of such property.

## 5. Independent Contractor

The Parties agree and acknowledge that Independent Contractor is an independent contractor and is not, for any purpose, an employee of Client. Independent Contractor does not have authority to enter into agreements or contracts on behalf of Client. Independent Contractor shall not be entitled to any of Client's benefits, including, but not limited to, health insurance, retirement or other plans. Client shall not be obligated to pay worker's compensation, unemployment compensation, social security tax, withholding tax, or other taxes for or on behalf of Independent Contractor in connection with the performance of this Agreement.

## 6. Confidentiality

a. Confidential and Proprietary Information
Independent Contractor will have access to Client's confidential information ("Confidential Information"), which includes sensitive, non-public, proprietary data.

b. Confidentiality Obligations
Independent Contractor shall not disclose or use any Confidential Information without Client's written consent.

c. Rights in Confidential Information
Confidential Information is Client's sole property. Independent Contractor does not gain ownership.

d. Irreparable Harm
Breach of confidentiality obligations shall result in irreparable harm. Client may seek equitable relief and damages.

## 7. Ownership of Work Product

All work product created by Independent Contractor under this Agreement shall be the sole property of Client. Independent Contractor assigns all copyrights and related rights in the Work Product to Client.

## 8. Insurance

Independent Contractor shall maintain insurance with appropriate coverage for liability, property damage, and losses connected to performance of the Services.

## 9. Non-Compete

Independent Contractor agrees not to compete with Client during the term of this Agreement and for \{\{contractFields.non_compete_period\}\} months following its termination.

## 10. Non-Solicit

Independent Contractor shall not solicit Client's employees, officers, or customers for \{\{contractFields.non_solicit_period\}\} months following termination.

## 11. Mutual Representations and Warranties

Both Parties represent that they have authority to enter this Agreement and that it is binding.

## 12. Independent Contractor Representation and Warranties

Independent Contractor warrants that it has all necessary permits and authority to perform Services in compliance with applicable laws.

## 13. Indemnification

Independent Contractor shall indemnify and hold harmless Client from claims, damages, and liabilities arising from Contractor's actions or breach of this Agreement.

## 14. Governing Law

This Agreement shall be governed exclusively by the laws of the State of \{\{contractFields.governing_state\}\}.

## 15. Disputes

Disputes shall first be attempted to be resolved through mediation. If unresolved, they will be settled by binding arbitration.

## 16. Binding Effect

This Agreement is binding on successors and permitted assigns.

## 17. Assignment

Independent Contractor may not assign this Agreement without Client's prior written consent.

## 18. Entire Agreement

This Agreement supersedes all prior negotiations and agreements.

## 19. Amendments

Any modifications must be in writing and signed by both Parties.

## 20. Notices

Notices shall be in writing and delivered by hand, courier, certified mail, or other agreed methods.

## 21. Waiver

No waiver of rights shall be valid unless in writing and signed.

## 22. Further Assurances

Parties shall execute documents as necessary to carry out this Agreement.

## 23. Severability

If any provision is invalid, the rest of the Agreement remains enforceable.

## IN WITNESS WHEREOF

This Agreement has been executed and delivered as of the Effective Date.

**Client Signature:** ___________________ **Client Full Name:** \{\{contractFields.client_signature_name\}\}
**Independent Contractor Signature:** ___________________ **Independent Contractor Full Name:** \{\{contractFields.contractor_signature_name\}\}`,
    fields: [
      { id: 'effective_date', label: 'Effective Date', type: 'date', placeholder: 'Select effective date', required: true },
      { id: 'services_description', label: 'Services Description', type: 'textarea', placeholder: 'Describe the services to be provided...', required: true },
      { id: 'compensation_amount', label: 'Compensation Amount', type: 'text', placeholder: 'e.g., $5,000, $100/hour', required: true },
      { id: 'start_date', label: 'Start Date', type: 'date', placeholder: 'Select start date', required: true },
      { id: 'end_date', label: 'End Date', type: 'date', placeholder: 'Select end date', required: true },
      { id: 'non_compete_period', label: 'Non-Compete Period (months)', type: 'number', placeholder: '12', required: true, defaultValue: '12' },
      { id: 'non_solicit_period', label: 'Non-Solicit Period (months)', type: 'number', placeholder: '12', required: true, defaultValue: '12' },
      { id: 'governing_state', label: 'Governing State', type: 'text', placeholder: 'e.g., California, New York', required: true, defaultValue: 'California' },
      { id: 'client_signature_name', label: 'Client Signature Name', type: 'text', placeholder: 'Full name for signature', required: true },
      { id: 'contractor_signature_name', label: 'Contractor Signature Name', type: 'text', placeholder: 'Full name for signature', required: true }
    ]
  },
  {
    id: 'nda',
    name: 'Non-Disclosure Agreement (NDA)',
    description: 'A comprehensive non-disclosure and confidentiality agreement for protecting confidential information',
    category: 'Legal',
    template: `
# NON-DISCLOSURE AND CONFIDENTIALITY AGREEMENT

This Non-Disclosure and Confidentiality Agreement (this "Agreement") is entered into as of \{\{contractFields.effective_date\}\} (the "Effective Date") by and between \{\{contractFields.disclosing_party_name\}\}, a \{\{contractFields.disclosing_party_type\}\} (the "Disclosing Party") and \{\{contractFields.receiving_party_name\}\} (the "Receiving Party").

Disclosing Party and Receiving Party have indicated an interest in exploring a potential business relationship (the "Transaction"). In connection with its respective evaluation of the Transaction, each party, their respective affiliates and their respective directors, officers, employees, agents or advisors (collectively, "Representatives") may provide or gain access to certain confidential and proprietary information. In consideration for being furnished Confidential Information, Disclosing Party and Receiving Party agree as follows:

## 1. Confidential Information

"Confidential Information" shall mean (a) all information relating to Disclosing Party's products, business and operations including, but not limited to, financial documents and plans, customers, suppliers, manufacturing partners, marketing strategies, vendors, products, product development plans, technical product data, product samples, costs, sources, strategies, operations procedures, proprietary concepts, inventions, sales leads, sales data, customer lists, customer profiles, technical advice or knowledge, contractual agreements, price lists, supplier lists, sales estimates, product specifications, trade secrets, distribution methods, inventories, marketing strategies, source code, software, algorithms, data, drawings or schematics, blueprints, computer programs and systems and know-how or other intellectual property of Disclosing Party and its affiliates that may be at any time furnished, communicated or delivered by Disclosing Party to Receiving Party, whether in oral, tangible, electronic or other form; (b) the terms of any agreement, including this Agreement, and the discussions, negotiations and proposals related to any agreement; (c) information acquired during any tours of Disclosing Party's facilities; and (d) all other non-public information provided by Disclosing Party whatsoever. All Confidential Information shall remain the property of Disclosing Party.

## 2. Exclusions from Confidential Information

The obligation of confidentiality with respect to Confidential Information will not apply to any information:

a. If the information is or becomes publicly known and available other than as a result of prior unauthorized disclosure by Receiving Party or any of its Representatives;

b. If the information is or was received by Receiving Party from a third party source which, to the best knowledge of Receiving Party or its Representatives, is or was not under a confidentiality obligation to Disclosing Party with respect to such information;

c. If the information is disclosed by Receiving Party with the Disclosing Party's prior written permission.

d. If the information is independently developed by Receiving Party prior to disclosure by Disclosing Party and without the use and benefit of any of the Disclosing Party's Confidential Information; or

e. If Receiving Party or any of its Representatives is legally compelled by applicable law, by any court, governmental agency or regulatory authority or by subpoena or discovery request in pending litigation but only if, to the extent lawful, Receiving Party or its Representatives give prompt written notice of that fact to Disclosing Party prior to disclosure so that Disclosing Party may request a protective order or other remedy to prevent or limit such disclosure.

## 3. Obligation to Maintain Confidentiality

a. Receiving Party and its Representatives agree to retain the Confidential Information of the Disclosing Party in strict confidence and not to disclose any Confidential Information to any third party without the prior written consent of the Disclosing Party.

b. Receiving Party and its Representatives shall adopt and/or maintain security processes and procedures to safeguard the confidentiality of the Confidential Information and shall take all reasonable precautions to protect the Confidential Information against theft, loss, or unauthorized disclosure.

c. Upon the termination of this Agreement, Receiving Party will ensure that all documents, memoranda, notes and other writings or electronic records prepared by it that contain Confidential Information are either returned to the Disclosing Party or destroyed.

d. If there is an unauthorized disclosure or loss of any Confidential Information, Receiving Party will promptly, at its own expense, notify Disclosing Party in writing of such unauthorized disclosure or loss and take all reasonable steps to prevent further unauthorized disclosure or loss.

## 4. Non-Disclosure of Transaction

Without Disclosing Party's prior written consent, neither Receiving Party nor its Representatives shall disclose to any other person the fact that Confidential Information has been made available to it, that discussions or negotiations are taking place concerning a Transaction, or any of the terms, conditions or other facts with respect to any such discussions or negotiations, including the status thereof.

## 5. Representatives

Receiving Party will take reasonable steps to ensure that its Representatives adhere to the terms of this Agreement. Receiving Party will be responsible for any breach of this Agreement by its Representatives.

## 6. Disclaimer

There is no representation or warranty, express or implied, made by Disclosing Party as to the accuracy or completeness of the Confidential Information. Disclosing Party shall have no liability to Receiving Party resulting from the use of the Confidential Information.

## 7. Remedies

Each party agrees that use or disclosure of any Confidential Information in a manner inconsistent with this Agreement will give rise to irreparable injury to the other party inadequately compensable in damages. Accordingly, either party may seek and obtain injunctive relief against the breach or threatened breach of the foregoing undertakings, in addition to any other legal remedies which may be available.

## 8. Notices

All notices given under this Agreement must be in writing and delivered to the parties at their respective addresses set forth below or at such other address as may be designated by written notice.

## 9. Termination

This Agreement will terminate on the earlier of: (a) the written agreement of the parties; (b) consummation of the Transaction; or (c) \{\{contractFields.termination_period\}\} from the date hereof.

## 10. Amendment

This Agreement may be amended or modified only by a written agreement signed by both of the parties.

## 11. Jurisdiction

This Agreement will be governed by and construed in accordance with the laws of the State of \{\{contractFields.governing_state\}\}, without regard to its conflict of law principles.

## 12. Miscellaneous

This Agreement will inure to the benefit of and be binding on the respective successors and permitted assigns of the parties. This Agreement constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior agreements and understandings, both written and oral, between the parties with respect to the subject matter hereof.

## Signatures

**DISCLOSING PARTY:**
_________________________
\{\{contractFields.disclosing_party_representative\}\}
\{\{contractFields.disclosing_party_title\}\}
Date: _______________

**RECEIVING PARTY:**
_________________________
\{\{contractFields.receiving_party_representative\}\}
\{\{contractFields.receiving_party_title\}\}
Date: _______________
    `,
    fields: [
      { id: 'effective_date', label: 'Effective Date', type: 'date', placeholder: 'Select effective date', required: true },
      { id: 'disclosing_party_name', label: 'Disclosing Party Name', type: 'text', placeholder: 'Enter disclosing party name', required: true },
      { id: 'disclosing_party_type', label: 'Disclosing Party Type', type: 'text', placeholder: 'e.g., corporation, LLC, individual', required: true },
      { id: 'receiving_party_name', label: 'Receiving Party Name', type: 'text', placeholder: 'Enter receiving party name', required: true },
      { id: 'termination_period', label: 'Termination Period', type: 'text', placeholder: 'e.g., 2 years, 3 years', required: true, defaultValue: '2 years' },
      { id: 'governing_state', label: 'Governing State', type: 'text', placeholder: 'e.g., California, New York', required: true, defaultValue: 'California' },
      { id: 'disclosing_party_representative', label: 'Disclosing Party Representative', type: 'text', placeholder: 'Full name of representative', required: true },
      { id: 'disclosing_party_title', label: 'Disclosing Party Title', type: 'text', placeholder: 'Title of representative', required: true },
      { id: 'receiving_party_representative', label: 'Receiving Party Representative', type: 'text', placeholder: 'Full name of representative', required: true },
      { id: 'receiving_party_title', label: 'Receiving Party Title', type: 'text', placeholder: 'Title of representative', required: true }
    ]
  },
  {
    id: 'licensing-agreement',
    name: 'Licensing Agreement',
    description: 'A comprehensive licensing agreement for intellectual property rights',
    category: 'Legal',
    template: `# LICENSING AGREEMENT

This License Agreement (this "Agreement") is made as of {{contractFields.effective_date}} (the "Effective Date") by and among {{contractFields.owner_name}} ("Owner") and {{contractFields.user_name}} (collectively, "User").

## 1. License

Owner hereby grants to user to use the intellectual property described in Exhibit A attached hereto and incorporated by reference (the "Licensed IP"), solely for the limited purposes of: {{contractFields.license_purposes}}.

User is authorized to use the Licensed IP in the following regions: {{contractFields.authorized_regions}}.

Nothing herein obligates User to exercise the rights granted in this Agreement.

## 1A. Restrictions on Use

The User may use the Licensed IP only as expressly permitted under this Agreement. The User shall not:

- Copy, modify, adapt, or create derivative works based on the Licensed IP;
- Sell, sublicense, distribute, or otherwise transfer the Licensed IP to any third party without the Owner's prior written consent;
- Use the Licensed IP in connection with any unlawful, infringing, or defamatory materials or activities;
- Reverse engineer, decompile, or disassemble any software or proprietary elements included in the Licensed IP;
- Remove, obscure, or alter any copyright, trademark, or other proprietary notices.

Any use of the Licensed IP outside the scope of this Agreement shall constitute a material breach and may result in immediate termination of the license.

## 2. Consideration

As consideration for the license granted and described in this Agreement, User shall pay to Owner the following fees and/or royalties:

**Type of Payment:** {{contractFields.payment_type}}  
**Payment Due Date:** {{contractFields.payment_due_date}}  
**Payment Amount:** {{contractFields.payment_amount}}

Payment shall be made within {{contractFields.payment_terms}} days of the due date. In the event any payment is collected at law or through an attorney-at-law, or under advice therefrom, or through a collection agency, User agrees to pay all costs of collection, including, without limitation, all court costs and reasonable attorney's fees.

## 3. Right to Sublicense

User has no right to grant sublicenses to any third party unless Owner provides its approval in writing. Any approved sublicense is subordinate to, and must conform to the terms and conditions of this Agreement, and will not include the right to grant further sublicenses.

## 4. Copies

User shall not make copies of the Licensed IP, except as expressly approved by Owner. For any authorized copy, User must accurately reproduce the Licensed IP with proper notices as directed by Owner.

## 5. Intellectual Property Notice and Markings

User shall not remove any copyright or trademark notices. Owner may require appropriate notices on all products and promotional materials.

## 6. Ownership of Licensed IP

User agrees that the Owner is, and will remain, the sole and exclusive owner of all rights to the Licensed IP and copies thereof.

## 7. User's Diligence

User agrees to diligently protect the Licensed IP and notify Owner of any infringement, misappropriation, or violation.

## 8. Maintenance and Enforcement

Owner has sole discretion regarding maintenance, renewal, and enforcement of Licensed IP.

## 9. Enforcement Actions

If Owner initiates any legal action regarding infringement, User shall fully cooperate, including providing evidence or being named in the action if required. Owner has sole control over such actions.

## 10. Mutual Representations and Warranties

Each party represents it has the authority to enter into this Agreement and that it is valid and binding.

## 11. Owner's Representations and Warranties

Owner represents that it owns or controls the Licensed IP, to the best of its knowledge does not infringe on third-party rights, and has no knowledge of claims contrary to this warranty.

## 12. No Warranties

User acknowledges that the Licensed IP is provided "as is" without warranty of any kind.

## 13. Laws and Regulations

User agrees to comply with all applicable laws.

## 14. Indemnification by Owner

Owner will not indemnify User. User holds Owner harmless from liabilities arising out of User's or sublicensees' use of Licensed IP.

## 15. Limitations of Liability

Neither party will be liable for indirect, incidental, or consequential damages, except as permitted by law.

## 16. Term

Agreement commences on Effective Date and continues for {{contractFields.agreement_term}} years.

## 17. Termination

Either party may terminate this Agreement upon written notice if the other party materially breaches and fails to cure within {{contractFields.cure_period}} days. Termination is without prejudice to prior liabilities.

## 18. Assignment

User may not assign this Agreement without Owner's prior written consent. Owner may assign to affiliates or successors.

## 19. Severability

If any provision is invalid, the remainder shall remain enforceable.

## 20. No Waiver

No waiver is valid unless in writing signed by both parties.

## 21. Entire Agreement

This Agreement and attachments represent the entire agreement and supersede prior understandings.

## 22. Governing Law

This Agreement will be governed by the laws of the State of {{contractFields.governing_state}}.

## 23. Dispute Resolution

Any dispute shall be resolved through {{contractFields.dispute_resolution}}.

## 24. Notices

Notices must be in writing and delivered as specified. Notices are deemed received when {{contractFields.notice_delivery}}.

## 25. Execution in Counterparts; Electronic Signatures

This Agreement may be executed in counterparts and signed electronically (DocuSign, Adobe Sign, etc.), which shall have the same effect as original signatures.

## Signatures

**IN WITNESS WHEREOF, the parties have entered into this Agreement as of the Effective Date.**

**Owner Signature:** ___________________ **Owner Full Name:** {{contractFields.owner_signature_name}}  
**User Signature:** ___________________ **User Full Name:** {{contractFields.user_signature_name}}`,
    fields: [
      { id: 'effective_date', label: 'Effective Date', type: 'date', placeholder: 'Select effective date', required: true },
      { id: 'owner_name', label: 'Owner Name', type: 'text', placeholder: 'Enter owner name', required: true },
      { id: 'user_name', label: 'User Name', type: 'text', placeholder: 'Enter user name', required: true },
      { id: 'license_purposes', label: 'License Purposes', type: 'textarea', placeholder: 'Describe the limited purposes for which the IP may be used', required: true },
      { id: 'authorized_regions', label: 'Authorized Regions', type: 'text', placeholder: 'e.g., United States, Canada, Europe', required: true },
      { id: 'payment_type', label: 'Payment Type', type: 'text', placeholder: 'e.g., One-time fee, Royalty, Annual license', required: true },
      { id: 'payment_due_date', label: 'Payment Due Date', type: 'date', placeholder: 'Select payment due date', required: true },
      { id: 'payment_amount', label: 'Payment Amount', type: 'text', placeholder: 'e.g., $5,000, 5% of revenue', required: true },
      { id: 'payment_terms', label: 'Payment Terms (Days)', type: 'number', placeholder: '30', required: true, defaultValue: '30' },
      { id: 'agreement_term', label: 'Agreement Term (Years)', type: 'number', placeholder: '5', required: true, defaultValue: '5' },
      { id: 'cure_period', label: 'Cure Period (Days)', type: 'number', placeholder: '30', required: true, defaultValue: '30' },
      { id: 'governing_state', label: 'Governing State', type: 'text', placeholder: 'e.g., California, New York', required: true, defaultValue: 'California' },
      { id: 'dispute_resolution', label: 'Dispute Resolution', type: 'text', placeholder: 'e.g., Arbitration, Mediation, Court', required: true, defaultValue: 'Arbitration' },
      { id: 'notice_delivery', label: 'Notice Delivery Method', type: 'text', placeholder: 'e.g., Email, Certified mail, Personal delivery', required: true, defaultValue: 'Email' },
      { id: 'owner_signature_name', label: 'Owner Signature Name', type: 'text', placeholder: 'Full name for signature', required: true },
      { id: 'user_signature_name', label: 'User Signature Name', type: 'text', placeholder: 'Full name for signature', required: true }
    ]
  }
];

export const getTemplateById = async (id: string): Promise<ContractTemplate | undefined> => {
  // For now, use hardcoded templates directly
  // TODO: Implement database fallback when templates are stored in DB
    return contractTemplates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: string): ContractTemplate[] => {
  return contractTemplates.filter(template => template.category === category);
};