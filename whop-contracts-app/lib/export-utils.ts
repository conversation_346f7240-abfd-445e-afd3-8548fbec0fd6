import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface ExportOptions {
  format: 'pdf' | 'docx';
  filename?: string;
  includeWatermark?: boolean;
  watermarkText?: string;
}

export async function exportToPDF(
  content: string,
  options: ExportOptions = { format: 'pdf' }
): Promise<void> {
  try {
    // Create a temporary div to render the content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    tempDiv.style.cssText = `
      position: absolute;
      left: -9999px;
      top: -9999px;
      width: 800px;
      background: white;
      padding: 40px;
      font-family: 'Times New Roman', serif;
      font-size: 14px;
      line-height: 1.6;
      color: #333;
    `;
    
    // Style headings
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      (heading as HTMLElement).style.cssText += `
        margin-top: 20px;
        margin-bottom: 10px;
        font-weight: bold;
      `;
    });

    // Style paragraphs
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach(p => {
      (p as HTMLElement).style.cssText += `
        margin-bottom: 10px;
      `;
    });

    document.body.appendChild(tempDiv);

    // Convert to canvas
    const canvas = await html2canvas(tempDiv, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      logging: false
    });

    // Remove temp div
    document.body.removeChild(tempDiv);

    // Create PDF
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');
    
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = pageWidth - 20; // 10mm margin on each side
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    let heightLeft = imgHeight;
    let position = 10; // 10mm top margin

    // Add first page
    pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - 20; // Account for margins

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight + 10;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
      heightLeft -= pageHeight - 20;
    }

    // Add watermark if requested
    if (options.includeWatermark && options.watermarkText) {
      const pageCount = pdf.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        pdf.setPage(i);
        pdf.setTextColor(200, 200, 200);
        pdf.setFontSize(50);
        pdf.text(options.watermarkText, pageWidth / 2, pageHeight / 2, {
          align: 'center',
          angle: 45,
        });
      }
    }

    // Download the PDF
    const filename = options.filename || `contract-${Date.now()}.pdf`;
    pdf.save(filename);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF');
  }
}

export function exportToDocx(content: string, options: ExportOptions = { format: 'docx' }): void {
  try {
    // Convert markdown-like content to HTML for Word
    let htmlContent = content
      .replace(/#{1}\s(.+)/g, '<h1>$1</h1>')
      .replace(/#{2}\s(.+)/g, '<h2>$1</h2>')
      .replace(/#{3}\s(.+)/g, '<h3>$1</h3>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br/>');

    // Wrap in paragraph tags
    htmlContent = `<p>${htmlContent}</p>`;

    // Create a complete HTML document with Word-friendly styling
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>Contract</title>
          <style>
            body {
              font-family: 'Times New Roman', serif;
              font-size: 12pt;
              line-height: 1.6;
              margin: 1in;
              color: #000;
            }
            h1, h2, h3 {
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
            }
            h1 { font-size: 18pt; }
            h2 { font-size: 16pt; }
            h3 { font-size: 14pt; }
            p {
              margin-bottom: 10px;
              text-align: justify;
            }
            strong {
              font-weight: bold;
            }
            em {
              font-style: italic;
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;

    // Create a blob and download
    const blob = new Blob([fullHtml], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    const filename = options.filename || `contract-${Date.now()}.doc`;
    
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting DOCX:', error);
    throw new Error('Failed to export DOCX');
  }
}

export function generateShareableLink(contractId: string): string {
  // Try to get the base URL from different sources
  let baseUrl = '';
  
  if (typeof window !== 'undefined') {
    // Client-side: use window.location.origin
    baseUrl = window.location.origin;
  } else if (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_APP_URL) {
    // Server-side: use environment variable
    baseUrl = process.env.NEXT_PUBLIC_APP_URL;
  } else if (typeof process !== 'undefined' && process.env.VERCEL_URL) {
    // Vercel deployment: use Vercel URL
    baseUrl = `https://${process.env.VERCEL_URL}`;
  } else {
    // Fallback: use localhost for development
    baseUrl = 'http://localhost:3000';
  }
  
  return `${baseUrl}/contract/${contractId}`;
}

export async function sendContractEmail(
  contractData: {
    clientEmail: string;
    clientName: string;
    contractContent: string;
    shareableLink: string;
  }
): Promise<boolean> {
  try {
    // This would integrate with your email service
    // For now, we'll simulate the email sending
    console.log('Sending contract email to:', contractData.clientEmail);
    console.log('Contract link:', contractData.shareableLink);
    
    // In a real implementation, you would:
    // 1. Use a service like SendGrid, Mailgun, or AWS SES
    // 2. Send an email with the contract content and signing link
    // 3. Include the PDF attachment if needed
    
    return true;
  } catch (error) {
    console.error('Error sending contract email:', error);
    return false;
  }
}