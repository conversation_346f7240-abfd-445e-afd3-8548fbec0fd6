import { notificationService } from './notifications';

export function initializeDemoNotifications() {
  // Add some demo notifications to show the system working
  setTimeout(() => {
    notificationService.addNotification({
      type: 'contract_viewed',
      title: 'Contract Viewed',
      message: '<EMAIL> has viewed the Service Agreement contract',
      contractId: 'contract_demo_001',
      clientEmail: '<EMAIL>',
    });
  }, 2000);

  setTimeout(() => {
    notificationService.addNotification({
      type: 'contract_signed',
      title: 'Contract Signed! 🎉',
      message: '<EMAIL> has signed the NDA contract',
      contractId: 'contract_demo_002',
      clientEmail: '<EMAIL>',
    });
  }, 5000);

  setTimeout(() => {
    notificationService.addNotification({
      type: 'invoice_paid',
      title: 'Payment Received! 💰',
      message: 'Payment of $2,500.00 has been received for invoice #INV001',
      contractId: 'contract_demo_003',
      invoiceId: 'invoice_demo_001',
    });
  }, 8000);
}

export function simulateContractActivity(contractId: string, clientEmail: string) {
  // Simulate contract being viewed after 3 seconds
  setTimeout(() => {
    notificationService.notifyContractViewed(contractId, clientEmail);
  }, 3000);

  // Simulate contract being signed after 10 seconds
  setTimeout(() => {
    notificationService.notifyContractSigned(contractId, clientEmail);
  }, 10000);
}