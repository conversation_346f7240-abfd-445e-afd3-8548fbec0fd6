import { whopSdk, isWhopReady } from './whop-sdk';
import { Invoice, ContractData } from './types';
import { generateInvoiceId } from './contract-utils';

export interface InvoiceData {
  amount: number;
  description: string;
  dueDate: Date;
  paymentMethod?: string;
  terms?: string;
  taxRate?: number;
  discountAmount?: number;
}

export interface WhopInvoiceRequest {
  amount: number;
  currency: 'USD';
  description: string;
  due_date: string;
  customer_email: string;
  customer_name: string;
  line_items: Array<{
    description: string;
    quantity: number;
    unit_price: number;
  }>;
  metadata?: Record<string, any>;
}

export async function createWhopInvoice(
  contractData: ContractData,
  invoiceData: InvoiceData,
  companyId: string
): Promise<Invoice> {
  try {
    // Calculate totals
    const subtotal = invoiceData.amount;
    const taxAmount = invoiceData.taxRate ? (subtotal * invoiceData.taxRate) / 100 : 0;
    const discount = invoiceData.discountAmount || 0;
    const total = subtotal + taxAmount - discount;

    // Prepare Whop invoice request
    const whopInvoiceRequest: WhopInvoiceRequest = {
      amount: Math.round(total * 100), // Convert to cents
      currency: 'USD',
      description: invoiceData.description,
      due_date: invoiceData.dueDate.toISOString(),
      customer_email: contractData.clientInfo.email,
      customer_name: contractData.clientInfo.name,
      line_items: [
        {
          description: invoiceData.description,
          quantity: 1,
          unit_price: Math.round(subtotal * 100), // Convert to cents
        }
      ],
      metadata: {
        contract_id: contractData.id,
        template_id: contractData.templateId,
        created_by: contractData.sellerInfo.name,
        company_id: companyId,
      }
    };

    // Create invoice using Whop SDK
    // Note: This is a placeholder as the actual Whop API endpoint may differ
    // if (isWhopReady()) {
    //   const whopInvoice = await whopSdk!.withCompany(companyId).invoices?.create?.(whopInvoiceRequest);
    // }

    // Create our internal invoice record
    const invoice: Invoice = {
      id: generateInvoiceId(),
      contractId: contractData.id,
      amount: total as any, // Type assertion for client-side usage
      dueDate: invoiceData.dueDate,
      status: 'PENDING',
      paymentMethod: invoiceData.paymentMethod || null,
      description: invoiceData.description,
      whopInvoiceId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return invoice;
  } catch (error) {
    console.error('Error creating Whop invoice:', error);
    throw new Error('Failed to create invoice');
  }
}

export function generateInvoiceHTML(
  invoice: Invoice,
  contractData: ContractData,
  invoiceData: InvoiceData
): string {
  const subtotal = invoiceData.amount;
  const taxAmount = invoiceData.taxRate ? (subtotal * invoiceData.taxRate) / 100 : 0;
  const discount = invoiceData.discountAmount || 0;
  const total = subtotal + taxAmount - discount;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <title>Invoice</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
          }
          .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #2563eb;
            margin: 0;
          }
          .invoice-number {
            font-size: 18px;
            color: #666;
            margin-top: 5px;
          }
          .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
          }
          .detail-section h3 {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          }
          .detail-section p {
            margin: 5px 0;
            color: #666;
          }
          .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .invoice-table th,
          .invoice-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
          }
          .invoice-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
          }
          .invoice-table .amount {
            text-align: right;
          }
          .invoice-totals {
            margin-left: auto;
            width: 300px;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
          }
          .total-row.final {
            font-weight: bold;
            font-size: 18px;
            border-bottom: 2px solid #2563eb;
            color: #2563eb;
          }
          .payment-terms {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
          }
          .payment-terms h3 {
            margin-top: 0;
            color: #333;
          }
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <div>
            <h1 class="invoice-title">INVOICE</h1>
            <p class="invoice-number">Invoice #${invoice.id.split('_')[1]?.substring(0, 8)}</p>
          </div>
          <div>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Due Date:</strong> ${invoice.dueDate.toLocaleDateString()}</p>
            <p><strong>Status:</strong> ${invoice.status.toUpperCase()}</p>
          </div>
        </div>

        <div class="invoice-details">
          <div class="detail-section">
            <h3>From:</h3>
            <p><strong>${contractData.sellerInfo.name}</strong></p>
            ${contractData.sellerInfo.businessName ? `<p>${contractData.sellerInfo.businessName}</p>` : ''}
            <p>${contractData.sellerInfo.address.replace(/\n/g, '<br/>')}</p>
            <p>${contractData.sellerInfo.email}</p>
            ${contractData.sellerInfo.phone ? `<p>${contractData.sellerInfo.phone}</p>` : ''}
          </div>

          <div class="detail-section">
            <h3>To:</h3>
            <p><strong>${contractData.clientInfo.name}</strong></p>
            ${contractData.clientInfo.businessName ? `<p>${contractData.clientInfo.businessName}</p>` : ''}
            <p>${contractData.clientInfo.address.replace(/\n/g, '<br/>')}</p>
            <p>${contractData.clientInfo.email}</p>
            ${contractData.clientInfo.phone ? `<p>${contractData.clientInfo.phone}</p>` : ''}
          </div>
        </div>

        <table class="invoice-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th class="amount">Unit Price</th>
              <th class="amount">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>${invoiceData.description}</td>
              <td>1</td>
              <td class="amount">$${subtotal.toFixed(2)}</td>
              <td class="amount">$${subtotal.toFixed(2)}</td>
            </tr>
          </tbody>
        </table>

        <div class="invoice-totals">
          <div class="total-row">
            <span>Subtotal:</span>
            <span>$${subtotal.toFixed(2)}</span>
          </div>
          ${discount > 0 ? `
          <div class="total-row">
            <span>Discount:</span>
            <span>-$${discount.toFixed(2)}</span>
          </div>
          ` : ''}
          ${taxAmount > 0 ? `
          <div class="total-row">
            <span>Tax (${invoiceData.taxRate}%):</span>
            <span>$${taxAmount.toFixed(2)}</span>
          </div>
          ` : ''}
          <div class="total-row final">
            <span>Total:</span>
            <span>$${total.toFixed(2)}</span>
          </div>
        </div>

        ${invoiceData.terms ? `
        <div class="payment-terms">
          <h3>Payment Terms</h3>
          <p>${invoiceData.terms}</p>
        </div>
        ` : ''}

        <div class="footer">
          <p>This invoice is linked to Contract #${contractData.id.split('_')[1]?.substring(0, 8)}</p>
          <p>The contract must be signed before payment can be processed.</p>
          <p>Thank you for your business!</p>
        </div>
      </body>
    </html>
  `;
}

export async function getInvoiceStatus(invoiceId: string): Promise<string> {
  try {
    // In a real implementation, this would check the Whop API for invoice status
    // For now, we'll return a placeholder
    return 'pending';
  } catch (error) {
    console.error('Error getting invoice status:', error);
    return 'unknown';
  }
}

export function validateInvoiceData(invoiceData: InvoiceData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!invoiceData.amount || invoiceData.amount <= 0) {
    errors.push('Invoice amount must be greater than 0');
  }

  if (!invoiceData.description || invoiceData.description.trim().length === 0) {
    errors.push('Invoice description is required');
  }

  if (!invoiceData.dueDate || invoiceData.dueDate < new Date()) {
    errors.push('Due date must be in the future');
  }

  if (invoiceData.taxRate && (invoiceData.taxRate < 0 || invoiceData.taxRate > 100)) {
    errors.push('Tax rate must be between 0 and 100');
  }

  if (invoiceData.discountAmount && invoiceData.discountAmount >= invoiceData.amount) {
    errors.push('Discount amount cannot be greater than or equal to the invoice amount');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}