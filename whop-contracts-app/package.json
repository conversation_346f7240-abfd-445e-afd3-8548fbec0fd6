{"name": "whop-nextjs-app-template", "version": "0.1.0", "private": true, "scripts": {"dev": "whop-proxy --command 'next dev'", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@supabase/supabase-js": "^2.58.0", "@types/jspdf": "^2.0.0", "@vercel/functions": "^2.0.3", "@whop/api": "^0.0.50", "@whop/react": "0.2.36", "docx-preview": "^0.3.6", "frosted-ui": "0.0.1-canary.79", "html2canvas": "^1.4.1", "jspdf": "^3.0.3", "lucide-react": "^0.544.0", "next": "15.3.2", "prisma": "^6.16.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.63.0", "zod": "^4.1.11"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@whop-apps/dev-proxy": "0.0.1-canary.116", "dotenv-cli": "^8.0.0", "tailwindcss": "^4", "tsx": "^4.20.6", "typescript": "^5"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}