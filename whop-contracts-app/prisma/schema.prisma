generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String             @id @default(cuid())
  whopId    String?            @unique
  email     String             @unique
  name      String?
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  templates ContractTemplate[]
  contracts Contract[]
  customers Customer[]

  @@map("users")
}

model Customer {
  id        String   @id @default(cuid())
  name      String
  email     String
  address   String?
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("customers")
}

model ContractTemplate {
  id          String     @id @default(cuid())
  name        String
  description String
  fields      Json
  category    String
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  authorId    String?
  template    String
  author      User?      @relation(fields: [authorId], references: [id])
  contracts   Contract[]

  @@map("contract_templates")
}

model Contract {
  id                String           @id @default(cuid())
  templateId        String
  contractFields    Json
  status            ContractStatus   @default(DRAFT)
  signedAt          DateTime?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  authorId          String
  clientInfo        Json
  clientSignature   String?
  clientSignatureAt DateTime?
  ipAddress         String?
  sellerInfo        Json
  userAgent         String?
  author            User             @relation(fields: [authorId], references: [id])
  template          ContractTemplate @relation(fields: [templateId], references: [id])
  invoices          Invoice[]
  notifications     Notification[]

  @@map("contracts")
}

model Invoice {
  id            String        @id @default(cuid())
  contractId    String
  whopInvoiceId String?
  amount        Decimal       @db.Decimal(10, 2)
  description   String
  dueDate       DateTime
  status        InvoiceStatus @default(PENDING)
  paymentMethod String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  contract      Contract      @relation(fields: [contractId], references: [id], onDelete: Cascade)

  @@map("invoices")
}

model Notification {
  id          String           @id @default(cuid())
  contractId  String?
  type        NotificationType
  title       String
  message     String
  isRead      Boolean          @default(false)
  createdAt   DateTime         @default(now())
  clientEmail String?
  invoiceId   String?
  contract    Contract?        @relation(fields: [contractId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum ContractStatus {
  DRAFT
  SENT
  SIGNED
  COMPLETED
}

enum InvoiceStatus {
  PENDING
  PAID
  OVERDUE
}

enum NotificationType {
  CONTRACT_VIEWED
  CONTRACT_SIGNED
  INVOICE_PAID
  CONTRACT_EXPIRED
  PAYMENT_REMINDER
}
