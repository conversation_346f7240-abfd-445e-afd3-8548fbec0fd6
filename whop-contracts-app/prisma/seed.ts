import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create contract templates
  const serviceAgreementTemplate = await prisma.contractTemplate.upsert({
    where: { id: 'service-agreement' },
    update: {},
    create: {
      id: 'service-agreement',
      name: 'Service Agreement',
      description: 'A comprehensive service agreement for freelancers and agencies',
      category: 'Service',
      template: `
# SERVICE AGREEMENT

**This Service Agreement** ("Agreement") is entered into on \{\{contract_date\}\} by and between:

**SERVICE PROVIDER:**
\{\{seller_name\}\}
\{\{seller_address\}\}
\{\{seller_email\}\}
\{\{seller_phone\}\}

**CLIENT:**
\{\{client_name\}\}
\{\{client_address\}\}
\{\{client_email\}\}
\{\{client_phone\}\}

## 1. SERVICES TO BE PROVIDED

The Service Provider agrees to provide the following services:

\{\{services_description\}\}

## 2. TIMELINE AND DELIVERABLES

**Project Start Date:** \{\{start_date\}\}
**Project End Date:** \{\{end_date\}\}

**Deliverables:**
\{\{deliverables\}\}

## 3. PAYMENT TERMS

**Total Project Cost:** $\{\{total_amount\}\}
**Payment Schedule:** \{\{payment_schedule\}\}
**Payment Method:** \{\{payment_method\}\}

Late payments may incur a fee of 1.5% per month on the outstanding balance.

## 4. INTELLECTUAL PROPERTY

Upon full payment, all intellectual property rights in the work product shall transfer to the Client, except for pre-existing intellectual property of the Service Provider.

## 5. CONFIDENTIALITY

Both parties agree to maintain confidentiality of all proprietary information disclosed during the course of this agreement.

## 6. TERMINATION

Either party may terminate this agreement with \{\{termination_notice\}\} days written notice.

## 7. LIABILITY

The Service Provider's liability shall not exceed the total amount paid under this agreement.

## 8. GOVERNING LAW

This agreement shall be governed by the laws of \{\{governing_law\}\}.

**SERVICE PROVIDER SIGNATURE:**
_________________________
{{seller_name}}
Date: _______________

**CLIENT SIGNATURE:**
_________________________
{{client_name}}
Date: _______________
      `,
      fields: [
        { id: 'contract_date', label: 'Contract Date', type: 'date', placeholder: 'Select date', required: true },
        { id: 'services_description', label: 'Services Description', type: 'textarea', placeholder: 'Describe the services to be provided...', required: true },
        { id: 'start_date', label: 'Start Date', type: 'date', placeholder: 'Select start date', required: true },
        { id: 'end_date', label: 'End Date', type: 'date', placeholder: 'Select end date', required: true },
        { id: 'deliverables', label: 'Deliverables', type: 'textarea', placeholder: 'List project deliverables...', required: true },
        { id: 'total_amount', label: 'Total Amount', type: 'number', placeholder: '0.00', required: true },
        { id: 'payment_schedule', label: 'Payment Schedule', type: 'textarea', placeholder: 'e.g., 50% upfront, 50% on completion', required: true },
        { id: 'payment_method', label: 'Payment Method', type: 'text', placeholder: 'e.g., Bank transfer, Credit card', required: true },
        { id: 'termination_notice', label: 'Termination Notice (days)', type: 'number', placeholder: '30', required: true, defaultValue: '30' },
        { id: 'governing_law', label: 'Governing Law', type: 'text', placeholder: 'e.g., State of California', required: true }
      ]
    }
  })

  const ndaTemplate = await prisma.contractTemplate.upsert({
    where: { id: 'nda' },
    update: {},
    create: {
      id: 'nda',
      name: 'Non-Disclosure Agreement (NDA)',
      description: 'A standard non-disclosure agreement for protecting confidential information',
      category: 'Legal',
      template: `
# NON-DISCLOSURE AGREEMENT

**This Non-Disclosure Agreement** ("Agreement") is entered into on \{\{contract_date\}\} by and between:

**DISCLOSING PARTY:**
{{seller_name}}
{{seller_address}}
{{seller_email}}

**RECEIVING PARTY:**
{{client_name}}
{{client_address}}
{{client_email}}

## 1. PURPOSE

The purpose of this Agreement is to protect confidential information that may be disclosed between the parties in connection with: \{\{purpose\}\}

## 2. DEFINITION OF CONFIDENTIAL INFORMATION

"Confidential Information" includes all information, whether written, oral, or in any other form, that is disclosed by the Disclosing Party to the Receiving Party, including but not limited to:

\{\{confidential_info_types\}\}

## 3. OBLIGATIONS OF RECEIVING PARTY

The Receiving Party agrees to:
- Hold all Confidential Information in strict confidence
- Not disclose Confidential Information to any third party without prior written consent
- Use Confidential Information solely for the purpose stated above
- Return or destroy all Confidential Information upon request

## 4. TERM

This Agreement shall remain in effect for \{\{term_years\}\} years from the date of execution, unless terminated earlier by mutual agreement.

## 5. REMEDIES

The Receiving Party acknowledges that any breach of this Agreement may cause irreparable harm, and the Disclosing Party shall be entitled to seek equitable relief, including injunction and specific performance.

## 6. GOVERNING LAW

This Agreement shall be governed by the laws of \{\{governing_law\}\}.

**DISCLOSING PARTY:**
_________________________
{{seller_name}}
Date: _______________

**RECEIVING PARTY:**
_________________________
{{client_name}}
Date: _______________
      `,
      fields: [
        { id: 'contract_date', label: 'Contract Date', type: 'date', placeholder: 'Select date', required: true },
        { id: 'purpose', label: 'Purpose of Disclosure', type: 'textarea', placeholder: 'Describe the purpose...', required: true },
        { id: 'confidential_info_types', label: 'Types of Confidential Information', type: 'textarea', placeholder: 'List types of confidential information...', required: true },
        { id: 'term_years', label: 'Term (years)', type: 'number', placeholder: '2', required: true, defaultValue: '2' },
        { id: 'governing_law', label: 'Governing Law', type: 'text', placeholder: 'e.g., State of California', required: true }
      ]
    }
  })

  const licensingTemplate = await prisma.contractTemplate.upsert({
    where: { id: 'licensing-agreement' },
    update: {},
    create: {
      id: 'licensing-agreement',
      name: 'Licensing Agreement',
      description: 'A licensing agreement for intellectual property and software',
      category: 'Licensing',
      template: `
# LICENSING AGREEMENT

**This Licensing Agreement** ("Agreement") is entered into on \{\{contract_date\}\} by and between:

**LICENSOR:**
{{seller_name}}
{{seller_address}}
{{seller_email}}

**LICENSEE:**
{{client_name}}
{{client_address}}
{{client_email}}

## 1. GRANT OF LICENSE

The Licensor hereby grants to the Licensee a \{\{license_type\}\} license to use the following:

**Licensed Property:** \{\{licensed_property\}\}

## 2. SCOPE OF LICENSE

This license permits the Licensee to:
\{\{permitted_uses\}\}

## 3. RESTRICTIONS

The Licensee may NOT:
\{\{restrictions\}\}

## 4. TERM AND TERMINATION

**License Term:** \{\{license_term\}\}
**Termination:** This license may be terminated \{\{termination_conditions\}\}

## 5. ROYALTIES AND PAYMENTS

**License Fee:** $\{\{license_fee\}\}
**Royalty Rate:** \{\{royalty_rate\}\}%
**Payment Schedule:** \{\{payment_schedule\}\}

## 6. INTELLECTUAL PROPERTY

The Licensor retains all right, title, and interest in and to the Licensed Property. This Agreement does not transfer ownership of any intellectual property.

## 7. WARRANTIES AND DISCLAIMERS

The Licensed Property is provided "AS IS" without any warranties, express or implied.

## 8. LIMITATION OF LIABILITY

In no event shall the Licensor be liable for any indirect, incidental, or consequential damages.

## 9. GOVERNING LAW

This Agreement shall be governed by the laws of \{\{governing_law\}\}.

**LICENSOR:**
_________________________
{{seller_name}}
Date: _______________

**LICENSEE:**
_________________________
{{client_name}}
Date: _______________
      `,
      fields: [
        { id: 'contract_date', label: 'Contract Date', type: 'date', placeholder: 'Select date', required: true },
        { id: 'license_type', label: 'License Type', type: 'text', placeholder: 'e.g., non-exclusive, exclusive', required: true, defaultValue: 'non-exclusive' },
        { id: 'licensed_property', label: 'Licensed Property', type: 'textarea', placeholder: 'Describe what is being licensed...', required: true },
        { id: 'permitted_uses', label: 'Permitted Uses', type: 'textarea', placeholder: 'List what the licensee can do...', required: true },
        { id: 'restrictions', label: 'Restrictions', type: 'textarea', placeholder: 'List what the licensee cannot do...', required: true },
        { id: 'license_term', label: 'License Term', type: 'text', placeholder: 'e.g., 1 year, perpetual', required: true },
        { id: 'termination_conditions', label: 'Termination Conditions', type: 'textarea', placeholder: 'Under what conditions can the license be terminated...', required: true },
        { id: 'license_fee', label: 'License Fee', type: 'number', placeholder: '0.00', required: true },
        { id: 'royalty_rate', label: 'Royalty Rate (%)', type: 'number', placeholder: '0', required: false, defaultValue: '0' },
        { id: 'payment_schedule', label: 'Payment Schedule', type: 'text', placeholder: 'e.g., Annual, One-time', required: true },
        { id: 'governing_law', label: 'Governing Law', type: 'text', placeholder: 'e.g., State of California', required: true }
      ]
    }
  })

  console.log('✅ Contract templates seeded successfully!')
  console.log(`   - Service Agreement: ${serviceAgreementTemplate.id}`)
  console.log(`   - NDA: ${ndaTemplate.id}`)
  console.log(`   - Licensing Agreement: ${licensingTemplate.id}`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
