# Whop App Configuration
# Get these values from your Whop Developer Dashboard
NEXT_PUBLIC_WHOP_APP_ID=your_app_id_here
WHOP_API_KEY=your_api_key_here
NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_agent_user_id_here
NEXT_PUBLIC_WHOP_COMPANY_ID=your_company_id_here

# Database Configuration
# For development, you can use a local PostgreSQL database or a cloud service
DATABASE_URL="postgresql://username:password@localhost:5432/whop_contracts"

# Supabase Configuration (if using Supabase)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Email Service Configuration (optional)
EMAIL_SERVICE_API_KEY=your_email_service_key_here

# Webhook Configuration (optional)
WHOP_WEBHOOK_SECRET=your_webhook_secret_here
