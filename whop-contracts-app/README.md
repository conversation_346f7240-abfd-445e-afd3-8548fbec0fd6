# Whop Contracts App

The Whop Contract + Invoice Generator allows sellers/creators on <PERSON>p to quickly generate legally binding contracts using pre-defined templates, automatically fill in client and seller details, and export them in a shareable format. Once a contract is finalized, sellers can generate and attach invoices (leveraging <PERSON><PERSON>'s API) to be sent alongside the contract for payment collection.

## ✨ Features

### 🏛️ Contract Template Library
- **Predefined Templates**: Service Agreement, NDA, Licensing Agreement
- **Professional Formatting**: Legally reviewed templates with clean, professional styling
- **Easy Selection**: Browse templates by category with detailed descriptions

### 🔧 Contract Builder
- **Step-by-Step Wizard**: Intuitive 3-step process for contract creation
- **Auto-Fill Seller Info**: Automatically populate seller details from Whop profile
- **Client Information**: Comprehensive client contact and business details
- **Customizable Terms**: Editable contract sections for dates, deliverables, payment terms
- **Live Preview**: Real-time preview before finalizing

### 📤 Export & Sharing
- **Multiple Formats**: Export to PDF and Word (DOCX) formats
- **Professional Layout**: Clean, print-ready formatting
- **Shareable Links**: Generate secure links for client review and signing
- **Download Options**: Direct download or email delivery

### 💰 Invoice Generation
- **Whop API Integration**: Seamless integration with <PERSON><PERSON>'s billing system
- **Automatic Linking**: Invoices automatically linked to contracts
- **Flexible Pricing**: Support for taxes, discounts, and multiple payment methods
- **Professional Invoices**: Branded invoice templates with comprehensive details
- **Payment Enforcement**: Contracts must be signed before invoice payment

### 🔔 Notifications & Tracking
- **Real-Time Updates**: Live notification system for contract activities
- **Status Tracking**: Monitor contract views, signatures, and payments
- **Email Notifications**: Automatic email updates for clients and sellers
- **Activity History**: Complete audit trail of all contract interactions

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and pnpm
- Whop Developer Account
- Whop App configured in the developer dashboard

### Installation

1. **Install dependencies:**
   ```bash
   pnpm install
   ```

2. **Configure Whop App:**
   - Create a Whop App on your [Whop Developer Dashboard](https://whop.com/dashboard/developer/)
   - Set the "Dashboard path" to `/dashboard/[companyId]`
   - Set the "App path" to `/experiences/[experienceId]`
   - Set the "Discover path" to `/discover`

3. **Environment Setup:**
   ```bash
   cp .env.development .env.local
   ```
   Update `.env.local` with your actual Whop app credentials:
   - `NEXT_PUBLIC_WHOP_APP_ID`
   - `WHOP_API_KEY`
   - `NEXT_PUBLIC_WHOP_AGENT_USER_ID`
   - `NEXT_PUBLIC_WHOP_COMPANY_ID`

4. **Start Development Server:**
   ```bash
   pnpm dev
   ```

5. **Access the App:**
   - Open your browser and navigate to your Whop company
   - Add the app to your company's tools section
   - Access via the dashboard at `/dashboard/[companyId]`

## 🏗️ Architecture

### Frontend Components
```
components/
├── contracts-dashboard.tsx     # Main dashboard with navigation
├── contract-builder.tsx       # Multi-step contract creation wizard
├── contracts-list.tsx         # Contract management and tracking
├── invoice-generator.tsx       # Invoice creation interface
└── notifications-panel.tsx    # Real-time notification system
```

### Core Libraries
```
lib/
├── contract-templates.ts      # Template definitions and processing
├── contract-utils.ts          # Contract processing utilities
├── invoice-utils.ts           # Invoice generation and Whop API integration
├── export-utils.ts            # PDF/DOCX export functionality
├── notifications.ts           # Notification service and email integration
├── types.ts                   # TypeScript type definitions
└── whop-sdk.ts               # Whop API configuration
```

### Key Features Implementation

#### Contract Templates
- Pre-defined legal templates with variable placeholders
- Dynamic field validation based on template requirements
- Professional formatting with automatic styling

#### Export System
- HTML to PDF conversion using jsPDF and html2canvas
- Word document generation with proper formatting
- Watermark and branding options

#### Invoice Integration
- Direct integration with Whop's billing API
- Support for line items, taxes, and discounts
- Automatic contract-invoice linking

#### Notification System
- Real-time in-app notifications
- Email notification service integration
- Activity tracking and audit trails

## 📋 Usage Guide

### Creating a Contract

1. **Select Template**: Choose from Service Agreement, NDA, or Licensing Agreement
2. **Seller Information**: Review and update your business details (auto-filled from Whop)
3. **Client Information**: Enter client contact and business information
4. **Contract Terms**: Customize specific terms, dates, and deliverables
5. **Preview & Export**: Review the contract and export in your preferred format

### Managing Contracts

- **View All Contracts**: Access comprehensive contract list with status tracking
- **Filter & Search**: Find contracts by client name, status, or contract ID
- **Status Updates**: Monitor draft, sent, signed, and completed contracts
- **Generate Invoices**: Create linked invoices for signed contracts

### Invoice Generation

1. **Select Signed Contract**: Choose a contract ready for invoicing
2. **Invoice Details**: Enter amount, description, due date, and payment terms
3. **Tax & Discounts**: Apply applicable taxes and discount amounts
4. **Generate & Send**: Create invoice and send to client via email

## 🔧 Configuration

### Whop API Integration
The app integrates with several Whop API endpoints:
- User authentication and profile data
- Company access control
- Invoice creation and management
- Notification delivery

### Email Service Setup
For production deployment, configure email service integration:
- SendGrid, Mailgun, or AWS SES for transactional emails
- SMTP configuration for contract and invoice delivery
- Email template customization

### Database Integration
For production use, integrate with a database:
- Contract metadata storage
- Client information management
- Invoice tracking and status updates
- Notification history

## 🚀 Deployment

### Vercel Deployment

1. **GitHub Repository**: Push your code to GitHub
2. **Vercel Import**: Import the repository on [Vercel](https://vercel.com/new)
3. **Environment Variables**: Configure all environment variables from `.env.local`
4. **Domain Configuration**: Update Whop app settings with your production domain
5. **Deploy**: Deploy and test the application

### Environment Variables for Production
```env
NEXT_PUBLIC_WHOP_APP_ID=your_app_id
WHOP_API_KEY=your_api_key
NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_agent_user_id
NEXT_PUBLIC_WHOP_COMPANY_ID=your_company_id
EMAIL_SERVICE_API_KEY=your_email_service_key
DATABASE_URL=your_database_connection_string
```

## 🔒 Security Considerations

- **Input Validation**: All form inputs are validated using Zod schemas
- **Authentication**: Whop SDK handles user authentication and authorization
- **Access Control**: Company-level access control for contract management
- **Data Sanitization**: Contract content is sanitized before export
- **Secure Links**: Contract sharing uses secure, time-limited tokens

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [Whop Developer Documentation](https://dev.whop.com/introduction)
- Open an issue in this repository
- Contact the development team

---

**Built with ❤️ for the Whop ecosystem**
