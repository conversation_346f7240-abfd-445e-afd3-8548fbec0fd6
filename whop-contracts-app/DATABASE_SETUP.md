# Database Setup Guide

This guide explains how to set up the database with Prisma and Supa<PERSON> for the Whop Contracts app.

## Prerequisites

- Supabase project with database credentials
- Node.js and pnpm installed

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ocadftlwiohrxxhactyb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jYWRmdGx3aW9ocnh4aGFjdHliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg5MTI2MjEsImV4cCI6MjA3NDQ4ODYyMX0.Qv0vyMRzGEwQOfGR8z9OTJZ0KnrocX0YZY68Ir1XeU8

# Database URLs
DATABASE_URL="***************************************************************************************************/postgres"
```

## Database Schema

The database includes the following models:

- **User**: Whop sellers/creators
- **ContractTemplate**: Predefined contract templates
- **Contract**: Individual contracts with seller/client info
- **Invoice**: Invoices linked to contracts
- **Notification**: System notifications

## Setup Commands

### 1. Generate Prisma Client
```bash
pnpm db:generate
```

### 2. Push Schema to Database
```bash
pnpm db:push
```

### 3. Seed Database with Templates
```bash
pnpm db:seed
```

## Database Operations

### Using the Database Service

The `DatabaseService` class in `lib/db.ts` provides convenient methods for common operations:

```typescript
import { DatabaseService } from '@/lib/db'

// Create a contract
const contract = await DatabaseService.createContract({
  templateId: 'service-agreement',
  sellerInfo: { name: 'John Doe', email: '<EMAIL>', address: '123 Main St' },
  clientInfo: { name: 'Jane Smith', email: '<EMAIL>', address: '456 Oak Ave' },
  contractFields: { total_amount: 5000, start_date: '2024-01-01' },
  authorId: 'user-id'
})

// Get contracts by author
const contracts = await DatabaseService.getContractsByAuthor('user-id')

// Create an invoice
const invoice = await DatabaseService.createInvoice({
  contractId: contract.id,
  amount: 5000,
  dueDate: new Date('2024-02-01'),
  description: 'Web development services'
})
```

### Using Prisma Client Directly

For more complex queries, use the Prisma client directly:

```typescript
import { prisma } from '@/lib/db'

const contractsWithInvoices = await prisma.contract.findMany({
  include: {
    template: true,
    author: true,
    invoices: true,
    notifications: true
  }
})
```

## Supabase Integration

The app uses Supabase for:

- **Authentication**: User management and sessions
- **Real-time**: Live updates for notifications and contract status
- **Storage**: File uploads for contract documents

### Supabase Client Usage

```typescript
import { supabase } from '@/lib/supabase'

// Get current user
const user = await supabase.auth.getUser()

// Subscribe to real-time changes
const subscription = supabase
  .channel('contracts')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'contracts' }, 
    (payload) => console.log('Contract updated:', payload)
  )
  .subscribe()
```

## Migration Notes

If you encounter connection issues:

1. **Check Supabase Status**: Ensure your Supabase project is active
2. **Verify Credentials**: Double-check the database URL and credentials
3. **Network Access**: Ensure your IP is allowed in Supabase settings
4. **Connection Pooling**: Use the direct connection URL for migrations

## Troubleshooting

### Common Issues

1. **Connection Timeout**: Check if the database URL is correct and the project is active
2. **Migration Conflicts**: Use `prisma db push` instead of `prisma migrate dev` for development
3. **Schema Drift**: Pull the current schema with `prisma db pull` to sync with existing database

### Useful Commands

```bash
# Check database connection
npx prisma db pull

# Reset database (DANGEROUS - only for development)
npx prisma migrate reset

# View current schema
npx prisma studio
```

## Next Steps

1. Set up authentication with Whop API
2. Implement real-time notifications
3. Add file upload functionality
4. Set up automated backups
5. Configure production environment variables
