Contracts App (PRD)
Product Name: Whop Contract + Invoice Generator
 Owner: [<PERSON> / <PERSON>]

 Version: 1.0

1. Overview
The Whop Contract + Invoice Generator will allow sellers/creators on Whop to quickly generate legally binding contracts using pre-defined templates, automatically fill in client and seller details, and export them in a shareable format. Once a contract is finalized, the seller can generate and attach an invoice (leveraging <PERSON><PERSON>’s API) to be sent alongside the contract for payment collection. They will need to sign the contract before being directed to pay the invoice

2. Goals & Objectives
Enable fast contract generation from curated templates.


Allow sellers to import or input seller + client details (business name, email, address, etc.).


Provide export functionality (PDF, Docx).


Integrate with Whop API to create/send invoices alongside the contract.


Reduce external dependency on third-party tools for contract/invoice generation.



3. Target Users
Whop Sellers / Creators: Freelancers, agencies, and businesses creating agreements with clients.


Clients: Buyers who receive contracts + invoices.



4. Key Features
4.1 Contract Template Library
Predefined templates (e.g., Service Agreement, NDA, Licensing Agreement).


Template selection step in the creation flow.


4.2 Contract Builder
Input fields for seller info (auto-filled from Whop seller profile if available).


Input fields for client info (name, email, business details).


Editable sections for contract terms (dates, deliverables, payment terms).


Preview mode before export.


4.3 Export & Sharing
Export options: PDF, Docx.


Option to download, email, or share contract link.


4.4 Invoice Generation (via Whop API)
Auto-generate invoice linked to the contract.


Invoice fields: amount, due date, payment method, terms.


Attach invoice to contract when sending.


API integration with Whop’s existing billing/invoicing endpoints.
Must sign contract before paying invoice
4.5 Notifications & Tracking
Seller notified when client views/signs the contract.


Client receives contract + invoice in one bundle.



5. Technical Requirements
Frontend: Whop app UI (React/Next.js).


Backend: Node.js/Express or Whop internal backend services.


Database: Store contract metadata (contract ID, template used, client info, invoice link).


Whop API Integration:


Contracts stored under seller’s Whop account.


Invoices generated via Whop’s billing API.

